from PySide6.QtCore import *
from PySide6.QtGui import *
from PySide6.QtWidgets import *
import os

class SettingsUI:
    def __init__(self, main_window, widgets, settings_manager):
        self.main_window = main_window
        self.widgets = widgets
        self.settings_manager = settings_manager
        self.setup_ui()

    def setup_ui(self):
        """Set up the settings UI."""
        # Clear existing content in the right box
        self.clear_right_box()

        # Apply current sidebar visibility settings
        show_movies = self.settings_manager.get_setting("ui", "show_movies_tab")
        show_series = self.settings_manager.get_setting("ui", "show_series_tab")
        show_settings = self.settings_manager.get_setting("ui", "show_settings_tab")

        print(f"[SETTINGS_UI] Current sidebar visibility: Movies={show_movies}, Series={show_series}, Settings={show_settings}")

        # Update sidebar buttons visibility with error handling
        try:
            self.widgets.btn_widgets.setVisible(show_movies)
            self.widgets.btn_new.setVisible(show_series)
            self.widgets.btn_save.setVisible(show_settings)
            print("[SETTINGS_UI] Applied sidebar visibility settings")
        except Exception as e:
            print(f"[SETTINGS_UI] Error applying sidebar visibility: {e}")

        # Create main layout
        self.settings_layout = QVBoxLayout()
        self.settings_layout.setContentsMargins(20, 20, 20, 20)
        self.settings_layout.setSpacing(15)

        # Add title
        self.title_label = QLabel("Settings")
        self.title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #bd93f9;
            margin-bottom: 15px;
            padding: 5px;
            border-bottom: 2px solid #44475a;
        """)
        self.title_label.setAlignment(Qt.AlignCenter)
        self.settings_layout.addWidget(self.title_label)

        # Create tab widget for settings categories
        self.settings_tabs = QTabWidget()
        # Establecer la posición de las pestañas en la parte superior (horizontal)
        self.settings_tabs.setTabPosition(QTabWidget.North)
        # Establecer tamaño mínimo para el widget de pestañas
        self.settings_tabs.setMinimumHeight(500)  # Altura mínima para mostrar todo el contenido
        self.settings_tabs.setMinimumWidth(550)   # Ancho mínimo para mostrar todo el contenido
        self.settings_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #44475a;
                background-color: #282a36;
            }
            QTabBar::tab {
                background-color: #282a36;
                color: #f8f8f2;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 80px;
                font-size: 12px;
            }
            QTabBar::tab:selected {
                background-color: #44475a;
                border-bottom: 3px solid #bd93f9;
            }
            QTabBar::tab:!selected {
                margin-top: 2px;
            }
            QTabBar {
                alignment: center;
            }
        """)

        # Create tabs
        self.create_general_tab()
        self.create_proxy_tab()
        self.create_ui_tab()
        self.create_download_tab()
        self.create_idm_tab()

        # Add tabs to layout
        self.settings_layout.addWidget(self.settings_tabs)

        # Create button layout for save and reset buttons
        button_layout = QHBoxLayout()

        # Add save button
        self.save_button = QPushButton("Save Settings")
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #bd93f9;
                color: white;
                border-radius: 4px;
                padding: 12px;
                font-weight: bold;
                font-size: 14px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #ff79c6;
            }
        """)
        self.save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(self.save_button)

        # Add reset button
        self.reset_button = QPushButton("Reset Settings")
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #44475a;
                color: white;
                border-radius: 4px;
                padding: 12px;
                font-weight: bold;
                font-size: 14px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #ff5555;
            }
        """)
        self.reset_button.clicked.connect(self.reset_settings)
        button_layout.addWidget(self.reset_button)

        # Add button layout to main layout
        self.settings_layout.addLayout(button_layout)

        # Create a widget to hold the layout
        self.settings_widget = QWidget()
        self.settings_widget.setLayout(self.settings_layout)

        # Add to the right box
        self.widgets.contentSettings.layout().addWidget(self.settings_widget)

    def clear_right_box(self):
        """Clear the content of the right box."""
        # Remove all widgets from the content settings layout
        layout = self.widgets.contentSettings.layout()
        if layout:
            while layout.count():
                item = layout.takeAt(0)
                widget = item.widget()
                if widget:
                    widget.deleteLater()

    def create_general_tab(self):
        """Create the general settings tab."""
        general_tab = QWidget()
        general_layout = QVBoxLayout(general_tab)
        general_layout.setContentsMargins(10, 10, 10, 10)
        general_layout.setSpacing(10)

        # Token group
        token_group = QGroupBox("Shahid Token")
        token_layout = QVBoxLayout(token_group)

        # Current token
        current_token = self.settings_manager.get_setting("token") or "No token set"
        token_label = QLabel(f"Current Token: {current_token[:10]}..." if len(current_token) > 10 else current_token)
        token_layout.addWidget(token_label)

        # New token input
        token_input_layout = QHBoxLayout()
        token_input_label = QLabel("New Token:")
        self.token_input = QLineEdit()
        self.token_input.setPlaceholderText("Enter your Shahid token")
        self.token_input.setEchoMode(QLineEdit.Password)
        token_input_layout.addWidget(token_input_label)
        token_input_layout.addWidget(self.token_input)
        token_layout.addLayout(token_input_layout)

        # Show token checkbox
        show_token_checkbox = QCheckBox("Show Token")
        show_token_checkbox.stateChanged.connect(self.toggle_token_visibility)
        token_layout.addWidget(show_token_checkbox)

        general_layout.addWidget(token_group)

        # Add stretch to push everything to the top
        general_layout.addStretch()

        self.settings_tabs.addTab(general_tab, "General")

    def create_proxy_tab(self):
        """Create the proxy settings tab."""
        proxy_tab = QWidget()
        proxy_layout = QVBoxLayout(proxy_tab)
        proxy_layout.setContentsMargins(15, 20, 15, 20)
        proxy_layout.setSpacing(15)

        # Estilo común para etiquetas
        label_style = """
            QLabel {
                font-size: 13px;
                color: #f8f8f2;
                min-width: 100px;
            }
        """

        # Estilo común para campos de texto
        input_style = """
            QLineEdit {
                background-color: #282a36;
                border: 1px solid #44475a;
                border-radius: 4px;
                padding: 8px;
                color: #f8f8f2;
                min-height: 30px;
            }
        """

        # Estilo común para combobox
        combo_style = """
            QComboBox {
                background-color: #282a36;
                border: 1px solid #44475a;
                border-radius: 4px;
                padding: 8px;
                min-height: 30px;
                color: #f8f8f2;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 30px;
                border-left: 1px solid #44475a;
            }
        """

        # Estilo para checkbox
        checkbox_style = """
            QCheckBox {
                font-size: 14px;
                color: #bd93f9;
                font-weight: bold;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """

        # Proxy configuration group
        proxy_config_group = QGroupBox("Proxy Configuration")
        proxy_config_group.setStyleSheet("QGroupBox { font-size: 14px; color: #bd93f9; padding-top: 15px; }")
        proxy_config_layout = QVBoxLayout(proxy_config_group)

        # Enable proxy
        self.proxy_enabled_checkbox = QCheckBox("Enable Proxy")
        self.proxy_enabled_checkbox.setStyleSheet(checkbox_style)
        self.proxy_enabled_checkbox.setChecked(self.settings_manager.get_setting("proxy", "enabled"))
        self.proxy_enabled_checkbox.stateChanged.connect(self.toggle_proxy_fields)
        proxy_config_layout.addWidget(self.proxy_enabled_checkbox)

        # Proxy type
        proxy_type_layout = QHBoxLayout()
        proxy_type_label = QLabel("Proxy Type:")
        proxy_type_label.setStyleSheet(label_style)
        self.proxy_type_combo = QComboBox()
        self.proxy_type_combo.setStyleSheet(combo_style)
        self.proxy_type_combo.addItems(["HTTP", "SOCKS4", "SOCKS5"])
        current_type = self.settings_manager.get_setting("proxy", "type").upper()
        index = self.proxy_type_combo.findText(current_type)
        if index >= 0:
            self.proxy_type_combo.setCurrentIndex(index)
        proxy_type_layout.addWidget(proxy_type_label)
        proxy_type_layout.addWidget(self.proxy_type_combo)
        proxy_config_layout.addLayout(proxy_type_layout)

        # Host and port
        proxy_host_layout = QHBoxLayout()
        proxy_host_label = QLabel("Host:")
        proxy_host_label.setStyleSheet(label_style)
        self.proxy_host_input = QLineEdit()
        self.proxy_host_input.setStyleSheet(input_style)
        self.proxy_host_input.setText(self.settings_manager.get_setting("proxy", "host"))
        proxy_host_layout.addWidget(proxy_host_label)
        proxy_host_layout.addWidget(self.proxy_host_input)
        proxy_config_layout.addLayout(proxy_host_layout)

        proxy_port_layout = QHBoxLayout()
        proxy_port_label = QLabel("Port:")
        proxy_port_label.setStyleSheet(label_style)
        self.proxy_port_input = QLineEdit()
        self.proxy_port_input.setStyleSheet(input_style)
        self.proxy_port_input.setText(str(self.settings_manager.get_setting("proxy", "port")))
        proxy_port_layout.addWidget(proxy_port_label)
        proxy_port_layout.addWidget(self.proxy_port_input)
        proxy_config_layout.addLayout(proxy_port_layout)

        proxy_layout.addWidget(proxy_config_group)

        # Authentication group
        auth_group = QGroupBox("Authentication (Optional)")
        auth_group.setStyleSheet("QGroupBox { font-size: 14px; color: #bd93f9; padding-top: 15px; }")
        auth_layout = QVBoxLayout(auth_group)

        # Username and password
        proxy_username_layout = QHBoxLayout()
        proxy_username_label = QLabel("Username:")
        proxy_username_label.setStyleSheet(label_style)
        self.proxy_username_input = QLineEdit()
        self.proxy_username_input.setStyleSheet(input_style)
        self.proxy_username_input.setText(self.settings_manager.get_setting("proxy", "username"))
        proxy_username_layout.addWidget(proxy_username_label)
        proxy_username_layout.addWidget(self.proxy_username_input)
        auth_layout.addLayout(proxy_username_layout)

        proxy_password_layout = QHBoxLayout()
        proxy_password_label = QLabel("Password:")
        proxy_password_label.setStyleSheet(label_style)
        self.proxy_password_input = QLineEdit()
        self.proxy_password_input.setStyleSheet(input_style)
        self.proxy_password_input.setEchoMode(QLineEdit.Password)
        self.proxy_password_input.setText(self.settings_manager.get_setting("proxy", "password"))
        proxy_password_layout.addWidget(proxy_password_label)
        proxy_password_layout.addWidget(self.proxy_password_input)
        auth_layout.addLayout(proxy_password_layout)

        proxy_layout.addWidget(auth_group)

        # Test proxy button
        self.test_proxy_button = QPushButton("Test Proxy Connection")
        self.test_proxy_button.setStyleSheet("""
            QPushButton {
                background-color: #44475a;
                color: white;
                border-radius: 4px;
                padding: 10px;
                font-weight: bold;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #6272a4;
            }
        """)
        self.test_proxy_button.clicked.connect(self.test_proxy_connection)
        proxy_layout.addWidget(self.test_proxy_button)

        # Add stretch to push everything to the top
        proxy_layout.addStretch()

        # Set initial state of proxy fields
        self.toggle_proxy_fields()

        self.settings_tabs.addTab(proxy_tab, "Proxy")

    def create_ui_tab(self):
        """Create the UI settings tab."""
        ui_tab = QWidget()
        ui_layout = QVBoxLayout(ui_tab)
        ui_layout.setContentsMargins(15, 20, 15, 20)
        ui_layout.setSpacing(15)

        # Estilo para checkbox
        checkbox_style = """
            QCheckBox {
                font-size: 13px;
                color: #f8f8f2;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """

        # Estilo para radio button
        radio_style = """
            QRadioButton {
                font-size: 13px;
                color: #f8f8f2;
                spacing: 10px;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
            }
        """

        # Sidebar visibility group
        sidebar_group = QGroupBox("Sidebar Visibility")
        sidebar_group.setStyleSheet("QGroupBox { font-size: 14px; color: #bd93f9; padding-top: 15px; }")
        sidebar_layout = QVBoxLayout(sidebar_group)
        sidebar_layout.setSpacing(10)

        # Show movies tab
        self.show_movies_checkbox = QCheckBox("Show Movies Tab")
        self.show_movies_checkbox.setStyleSheet(checkbox_style)
        self.show_movies_checkbox.setChecked(self.settings_manager.get_setting("ui", "show_movies_tab"))
        sidebar_layout.addWidget(self.show_movies_checkbox)

        # Show series tab
        self.show_series_checkbox = QCheckBox("Show Series Tab")
        self.show_series_checkbox.setStyleSheet(checkbox_style)
        self.show_series_checkbox.setChecked(self.settings_manager.get_setting("ui", "show_series_tab"))
        sidebar_layout.addWidget(self.show_series_checkbox)

        # Show settings tab
        self.show_settings_checkbox = QCheckBox("Show Settings Tab")
        self.show_settings_checkbox.setStyleSheet(checkbox_style)
        self.show_settings_checkbox.setChecked(self.settings_manager.get_setting("ui", "show_settings_tab"))
        sidebar_layout.addWidget(self.show_settings_checkbox)

        ui_layout.addWidget(sidebar_group)

        # Theme group
        theme_group = QGroupBox("Application Theme")
        theme_group.setStyleSheet("QGroupBox { font-size: 14px; color: #bd93f9; padding-top: 15px; }")
        theme_layout = QVBoxLayout(theme_group)
        theme_layout.setSpacing(10)

        # Theme selection
        theme_description = QLabel("Select the application theme:")
        theme_description.setStyleSheet("color: #f8f8f2; font-size: 13px;")
        theme_layout.addWidget(theme_description)

        # Radio buttons in horizontal layout
        theme_buttons_layout = QHBoxLayout()

        self.theme_radio_dark = QRadioButton("Dark Theme")
        self.theme_radio_dark.setStyleSheet(radio_style)
        self.theme_radio_light = QRadioButton("Light Theme")
        self.theme_radio_light.setStyleSheet(radio_style)

        current_theme = self.settings_manager.get_setting("ui", "theme")
        if current_theme == "light":
            self.theme_radio_light.setChecked(True)
        else:
            self.theme_radio_dark.setChecked(True)

        theme_buttons_layout.addWidget(self.theme_radio_dark)
        theme_buttons_layout.addWidget(self.theme_radio_light)
        theme_buttons_layout.addStretch()

        theme_layout.addLayout(theme_buttons_layout)

        # Theme preview
        theme_preview = QLabel("Theme changes will be applied after saving settings.")
        theme_preview.setStyleSheet("color: #6272a4; font-style: italic; font-size: 12px;")
        theme_layout.addWidget(theme_preview)

        ui_layout.addWidget(theme_group)

        # Add stretch to push everything to the top
        ui_layout.addStretch()

        self.settings_tabs.addTab(ui_tab, "UI")

    def create_download_tab(self):
        """Create the download settings tab."""
        download_tab = QWidget()
        download_layout = QVBoxLayout(download_tab)
        download_layout.setContentsMargins(15, 20, 15, 20)
        download_layout.setSpacing(15)

        # Estilo común para etiquetas
        label_style = """
            QLabel {
                font-size: 13px;
                color: #f8f8f2;
                min-width: 120px;
            }
        """

        # Estilo común para combobox
        combo_style = """
            QComboBox {
                background-color: #282a36;
                border: 1px solid #44475a;
                border-radius: 4px;
                padding: 8px;
                min-height: 30px;
                color: #f8f8f2;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 30px;
                border-left: 1px solid #44475a;
            }
        """

        # Estilo para botones
        button_style = """
            QPushButton {
                background-color: #44475a;
                color: white;
                border-radius: 4px;
                padding: 8px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #6272a4;
            }
        """

        # Default quality
        quality_group = QGroupBox("Download Quality")
        quality_group.setStyleSheet("QGroupBox { font-size: 14px; color: #bd93f9; padding-top: 15px; }")
        quality_group_layout = QVBoxLayout(quality_group)

        quality_layout = QHBoxLayout()
        quality_label = QLabel("Default Quality:")
        quality_label.setStyleSheet(label_style)
        self.quality_combo = QComboBox()
        self.quality_combo.setStyleSheet(combo_style)
        self.quality_combo.addItems(["2160p", "1080p", "720p", "480p", "360p"])
        current_quality = self.settings_manager.get_setting("download", "default_quality")
        index = self.quality_combo.findText(current_quality)
        if index >= 0:
            self.quality_combo.setCurrentIndex(index)
        quality_layout.addWidget(quality_label)
        quality_layout.addWidget(self.quality_combo)
        quality_group_layout.addLayout(quality_layout)

        download_layout.addWidget(quality_group)

        # Download path
        path_group = QGroupBox("Download Location")
        path_group.setStyleSheet("QGroupBox { font-size: 14px; color: #bd93f9; padding-top: 15px; }")
        path_group_layout = QVBoxLayout(path_group)

        path_layout = QHBoxLayout()
        path_label = QLabel("Download Path:")
        path_label.setStyleSheet(label_style)
        self.path_input = QLineEdit()
        self.path_input.setStyleSheet("""
            QLineEdit {
                background-color: #282a36;
                border: 1px solid #44475a;
                border-radius: 4px;
                padding: 8px;
                color: #f8f8f2;
                min-height: 30px;
            }
        """)
        self.path_input.setText(self.settings_manager.get_setting("download", "download_path"))
        path_button = QPushButton("Browse")
        path_button.setStyleSheet(button_style)
        path_button.clicked.connect(self.browse_download_path)
        path_layout.addWidget(path_label)
        path_layout.addWidget(self.path_input)
        path_layout.addWidget(path_button)
        path_group_layout.addLayout(path_layout)

        download_layout.addWidget(path_group)

        # Add stretch to push everything to the top
        download_layout.addStretch()

        self.settings_tabs.addTab(download_tab, "Download")

    def toggle_token_visibility(self, state):
        """Toggle the visibility of the token input."""
        if state == Qt.Checked:
            self.token_input.setEchoMode(QLineEdit.Normal)
        else:
            self.token_input.setEchoMode(QLineEdit.Password)

    def toggle_proxy_fields(self):
        """Enable or disable proxy fields based on checkbox state."""
        enabled = self.proxy_enabled_checkbox.isChecked()
        self.proxy_type_combo.setEnabled(enabled)
        self.proxy_host_input.setEnabled(enabled)
        self.proxy_port_input.setEnabled(enabled)
        self.proxy_username_input.setEnabled(enabled)
        self.proxy_password_input.setEnabled(enabled)
        self.test_proxy_button.setEnabled(enabled)

    def test_proxy_connection(self):
        """Test the proxy connection."""
        # Save current proxy settings to test
        self.save_proxy_settings()

        # Test connection
        success, message = self.settings_manager.test_proxy()

        # Show result
        if success:
            QMessageBox.information(self.main_window, "Proxy Test", message)
        else:
            QMessageBox.warning(self.main_window, "Proxy Test", message)

    def browse_download_path(self):
        """Open a dialog to select download path."""
        current_path = self.path_input.text()
        if not current_path:
            current_path = os.path.expanduser("~")

        path = QFileDialog.getExistingDirectory(
            self.main_window,
            "Select Download Directory",
            current_path
        )

        if path:
            self.path_input.setText(path)

    def save_proxy_settings(self):
        """Save proxy settings."""
        try:
            # Asegurarse de que la estructura de proxy sea correcta
            if not isinstance(self.settings_manager.settings.get("proxy"), dict):
                self.settings_manager.settings["proxy"] = {}

            # Guardar configuración de proxy
            self.settings_manager.set_setting("proxy", "enabled", self.proxy_enabled_checkbox.isChecked())
            self.settings_manager.set_setting("proxy", "type", self.proxy_type_combo.currentText().lower())
            self.settings_manager.set_setting("proxy", "host", self.proxy_host_input.text())
            self.settings_manager.set_setting("proxy", "port", self.proxy_port_input.text())
            self.settings_manager.set_setting("proxy", "username", self.proxy_username_input.text())
            self.settings_manager.set_setting("proxy", "password", self.proxy_password_input.text())
        except Exception as e:
            print(f"[UI] Error saving proxy settings: {e}")
            # Restaurar valores predeterminados para proxy
            self.settings_manager.settings["proxy"] = self.settings_manager.default_settings["proxy"].copy()

    def save_settings(self):
        """Save all settings."""
        try:
            # General settings - Token
            new_token = self.token_input.text().strip()
            if new_token:
                # Guardar token en el archivo token.txt - usar config del directorio raíz
                try:
                    # Go 3 levels up from modules/Shahid/settings_ui.py to reach root directory
                    root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    token_path = os.path.join(root_dir, 'config', 'shahid_token.txt')

                    # Crear directorio si no existe
                    os.makedirs(os.path.dirname(token_path), exist_ok=True)

                    # Guardar token en archivo
                    with open(token_path, 'w', encoding='utf-8') as f:
                        f.write(new_token)

                    print(f"[UI] Token saved to file: {token_path}")

                    # Actualizar token en la ventana principal
                    self.main_window.token = new_token

                    # Actualizar token en la configuración
                    self.settings_manager.set_setting("token", new_token)
                except Exception as token_error:
                    print(f"[UI] Error saving token to file: {token_error}")

            # Recopilar todas las configuraciones en un diccionario para guardarlas de una vez
            settings_batch = {}

            # Proxy settings
            settings_batch[("proxy", "enabled")] = self.proxy_enabled_checkbox.isChecked()
            settings_batch[("proxy", "type")] = self.proxy_type_combo.currentText().lower()
            settings_batch[("proxy", "host")] = self.proxy_host_input.text()
            settings_batch[("proxy", "port")] = self.proxy_port_input.text()
            settings_batch[("proxy", "username")] = self.proxy_username_input.text()
            settings_batch[("proxy", "password")] = self.proxy_password_input.text()

            # UI settings
            show_movies = self.show_movies_checkbox.isChecked()
            show_series = self.show_series_checkbox.isChecked()
            show_settings = self.show_settings_checkbox.isChecked()

            settings_batch[("ui", "show_movies_tab")] = show_movies
            settings_batch[("ui", "show_series_tab")] = show_series
            settings_batch[("ui", "show_settings_tab")] = show_settings

            # Tema
            theme = "light" if self.theme_radio_light.isChecked() else "dark"
            settings_batch[("ui", "theme")] = theme

            # Download settings
            settings_batch[("download", "default_quality")] = self.quality_combo.currentText()
            settings_batch[("download", "download_path")] = self.path_input.text()

            # IDM settings (only if IDM tab exists)
            if hasattr(self, 'idm_enabled_checkbox'):
                settings_batch[("idm", "enabled")] = self.idm_enabled_checkbox.isChecked()
                settings_batch[("idm", "custom_path")] = self.idm_path_input.text()
                settings_batch[("idm", "use_queue")] = self.idm_use_queue_checkbox.isChecked()
                settings_batch[("idm", "silent_mode")] = self.idm_silent_mode_checkbox.isChecked()

            # Guardar todas las configuraciones de una vez
            if hasattr(self.settings_manager, 'set_settings_batch'):
                self.settings_manager.set_settings_batch(settings_batch)
            else:
                # Método alternativo si set_settings_batch no está disponible
                for (key, subkey), value in settings_batch.items():
                    self.settings_manager.set_setting(key, value, subkey)

            # Aplicar inmediatamente la visibilidad de las pestañas
            try:
                self.widgets.btn_widgets.setVisible(show_movies)
                self.widgets.btn_new.setVisible(show_series)
                self.widgets.btn_save.setVisible(show_settings)
                print(f"[UI] Applied sidebar visibility: Movies={show_movies}, Series={show_series}, Settings={show_settings}")
            except Exception as e:
                print(f"[UI] Error applying sidebar visibility: {e}")

            # Forzar la escritura del archivo de configuración
            import time
            time.sleep(0.2)  # Pausa más larga para asegurar que el archivo se escriba

            # Verificar que el archivo existe y tiene contenido
            config_file = self.settings_manager.settings_file
            if os.path.exists(config_file):
                file_size = os.path.getsize(config_file)
                if file_size > 0:
                    print(f"[UI] Settings file verified: {config_file} ({file_size} bytes)")

                    # Leer el archivo para verificar su contenido
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            content = f.read()
                        print(f"[UI] Settings file content length: {len(content)} characters")
                    except Exception as read_error:
                        print(f"[UI] Error reading settings file: {read_error}")
                else:
                    print(f"[UI] Warning: Settings file is empty: {config_file}")
                    # Intentar guardar de nuevo
                    self.settings_manager.save_settings()
            else:
                print(f"[UI] Warning: Settings file does not exist: {config_file}")
                # Intentar guardar de nuevo
                self.settings_manager.save_settings()

            # Show success message
            QMessageBox.information(self.main_window, "Settings Saved", "Settings have been saved successfully.")

            # Apply remaining UI settings (theme)
            self.apply_ui_settings()

            # Update IDM button visibility
            if hasattr(self.main_window, 'update_idm_button_visibility'):
                self.main_window.update_idm_button_visibility()

            # No llamamos a apply_saved_settings de la ventana principal aquí
            # para evitar aplicar la configuración dos veces
        except Exception as e:
            print(f"[UI] Error saving settings: {e}")
            QMessageBox.warning(self.main_window, "Error Saving Settings",
                               f"An error occurred while saving settings: {str(e)}\n\nPlease try again.")
            # Intentar reparar la configuración
            self.settings_manager.settings = self.settings_manager.default_settings.copy()
            self.settings_manager.save_settings()

    def reset_settings(self):
        """Reset all settings to default values."""
        try:
            # Mostrar diálogo de confirmación
            result = QMessageBox.question(
                self.main_window,
                "Reset Settings",
                "Are you sure you want to reset all settings to default values? This cannot be undone.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if result == QMessageBox.Yes:
                # Resetear configuración
                if hasattr(self.settings_manager, 'reset_settings'):
                    success = self.settings_manager.reset_settings()
                    if success:
                        # Recargar configuración
                        self.settings_manager.settings = self.settings_manager.load_settings()

                        # Actualizar UI con los valores predeterminados
                        self.setup_ui()

                        # Actualizar la ventana principal
                        self.main_window.token = None
                        self.main_window.check_token()

                        # No llamamos a apply_saved_settings de la ventana principal aquí
                        # para evitar aplicar la configuración dos veces

                        QMessageBox.information(
                            self.main_window,
                            "Settings Reset",
                            "All settings have been reset to default values."
                        )
                    else:
                        QMessageBox.warning(
                            self.main_window,
                            "Reset Failed",
                            "Failed to reset settings. Please try again."
                        )
                else:
                    # Método alternativo si reset_settings no está disponible
                    self.settings_manager.settings = self.settings_manager.default_settings.copy()
                    self.settings_manager.save_settings()

                    # Actualizar UI con los valores predeterminados
                    self.setup_ui()

                    # Actualizar la ventana principal
                    self.main_window.token = None
                    self.main_window.check_token()

                    # No llamamos a apply_saved_settings de la ventana principal aquí
                    # para evitar aplicar la configuración dos veces

                    QMessageBox.information(
                        self.main_window,
                        "Settings Reset",
                        "All settings have been reset to default values."
                    )
        except Exception as e:
            print(f"[UI] Error resetting settings: {e}")
            QMessageBox.warning(
                self.main_window,
                "Reset Failed",
                f"An error occurred while resetting settings: {str(e)}"
            )

    def apply_ui_settings(self):
        """Apply UI settings."""
        try:
            # Apply theme only (sidebar visibility is applied immediately in save_settings)
            theme = self.settings_manager.get_setting("ui", "theme")
            try:
                if theme == "light":
                    # Apply light theme
                    theme_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "themes", "py_dracula_light.qss")
                    if os.path.exists(theme_path):
                        with open(theme_path, "r") as f:
                            self.main_window.setStyleSheet(f.read())
                    else:
                        print(f"[UI] Theme file not found: {theme_path}")
                else:
                    # Apply dark theme
                    theme_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "themes", "py_dracula_dark.qss")
                    if os.path.exists(theme_path):
                        with open(theme_path, "r") as f:
                            self.main_window.setStyleSheet(f.read())
                    else:
                        print(f"[UI] Theme file not found: {theme_path}")
            except Exception as e:
                print(f"[UI] Error applying theme: {e}")
        except Exception as e:
            print(f"[UI] Error applying UI settings: {e}")

    def create_idm_tab(self):
        """Create the IDM settings tab."""
        idm_tab = QWidget()
        idm_layout = QVBoxLayout(idm_tab)
        idm_layout.setContentsMargins(15, 20, 15, 20)
        idm_layout.setSpacing(15)

        # Common styles
        label_style = """
            QLabel {
                font-size: 13px;
                color: #f8f8f2;
                min-width: 120px;
            }
        """

        input_style = """
            QLineEdit {
                background-color: #282a36;
                border: 1px solid #44475a;
                border-radius: 4px;
                padding: 8px;
                color: #f8f8f2;
                min-height: 30px;
            }
        """

        checkbox_style = """
            QCheckBox {
                font-size: 13px;
                color: #f8f8f2;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """

        button_style = """
            QPushButton {
                background-color: #44475a;
                color: white;
                border-radius: 4px;
                padding: 10px;
                font-weight: bold;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #6272a4;
            }
        """

        # IDM Configuration group
        idm_config_group = QGroupBox("IDM Configuration")
        idm_config_group.setStyleSheet("QGroupBox { font-size: 14px; color: #bd93f9; padding-top: 15px; }")
        idm_config_layout = QVBoxLayout(idm_config_group)

        # Enable IDM
        self.idm_enabled_checkbox = QCheckBox("Enable IDM Integration")
        self.idm_enabled_checkbox.setStyleSheet(checkbox_style)
        self.idm_enabled_checkbox.setChecked(self.settings_manager.get_setting("idm", "enabled"))
        self.idm_enabled_checkbox.stateChanged.connect(self.toggle_idm_fields)
        idm_config_layout.addWidget(self.idm_enabled_checkbox)

        # IDM Path
        idm_path_layout = QHBoxLayout()
        idm_path_label = QLabel("IDM Path:")
        idm_path_label.setStyleSheet(label_style)
        self.idm_path_input = QLineEdit()
        self.idm_path_input.setStyleSheet(input_style)
        self.idm_path_input.setText(self.settings_manager.get_setting("idm", "custom_path"))
        self.idm_path_input.setPlaceholderText("Leave empty for auto-detection")

        self.idm_browse_button = QPushButton("Browse")
        self.idm_browse_button.setStyleSheet(button_style)
        self.idm_browse_button.clicked.connect(self.browse_idm_path)

        idm_path_layout.addWidget(idm_path_label)
        idm_path_layout.addWidget(self.idm_path_input)
        idm_path_layout.addWidget(self.idm_browse_button)
        idm_config_layout.addLayout(idm_path_layout)

        # IDM Options
        idm_options_layout = QVBoxLayout()

        self.idm_use_queue_checkbox = QCheckBox("Add to IDM queue instead of starting immediately")
        self.idm_use_queue_checkbox.setStyleSheet(checkbox_style)
        self.idm_use_queue_checkbox.setChecked(self.settings_manager.get_setting("idm", "use_queue"))
        idm_options_layout.addWidget(self.idm_use_queue_checkbox)

        self.idm_silent_mode_checkbox = QCheckBox("Use silent mode (no IDM dialogs)")
        self.idm_silent_mode_checkbox.setStyleSheet(checkbox_style)
        self.idm_silent_mode_checkbox.setChecked(self.settings_manager.get_setting("idm", "silent_mode"))
        idm_options_layout.addWidget(self.idm_silent_mode_checkbox)

        idm_config_layout.addLayout(idm_options_layout)

        idm_layout.addWidget(idm_config_group)

        # IDM Status group
        idm_status_group = QGroupBox("IDM Status")
        idm_status_group.setStyleSheet("QGroupBox { font-size: 14px; color: #bd93f9; padding-top: 15px; }")
        idm_status_layout = QVBoxLayout(idm_status_group)

        # Status label
        self.idm_status_label = QLabel("Checking IDM status...")
        self.idm_status_label.setStyleSheet("color: #f8f8f2; font-size: 13px;")
        idm_status_layout.addWidget(self.idm_status_label)

        # Test IDM button
        self.test_idm_button = QPushButton("Test IDM Connection")
        self.test_idm_button.setStyleSheet(button_style)
        self.test_idm_button.clicked.connect(self.test_idm_connection)
        idm_status_layout.addWidget(self.test_idm_button)

        idm_layout.addWidget(idm_status_group)

        # Add stretch to push everything to the top
        idm_layout.addStretch()

        # Set initial state of IDM fields
        self.toggle_idm_fields()
        self.update_idm_status()

        self.settings_tabs.addTab(idm_tab, "IDM")

    def toggle_idm_fields(self):
        """Toggle IDM fields based on enabled checkbox."""
        enabled = self.idm_enabled_checkbox.isChecked()
        self.idm_path_input.setEnabled(enabled)
        self.idm_browse_button.setEnabled(enabled)
        self.idm_use_queue_checkbox.setEnabled(enabled)
        self.idm_silent_mode_checkbox.setEnabled(enabled)
        self.test_idm_button.setEnabled(enabled)

    def browse_idm_path(self):
        """Browse for IDM executable."""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self.main_window,
            "Select IDM Executable",
            "",
            "Executable Files (*.exe);;All Files (*)"
        )
        if file_path:
            self.idm_path_input.setText(file_path)
            self.update_idm_status()

    def update_idm_status(self):
        """Update IDM status display."""
        try:
            # Check if IDM is available
            if hasattr(self.main_window, 'shahid_downloader'):
                # Update the IDM path in downloader if custom path is set
                custom_path = self.idm_path_input.text().strip()
                if custom_path:
                    self.main_window.shahid_downloader.idm_path = custom_path
                else:
                    self.main_window.shahid_downloader.idm_path = self.main_window.shahid_downloader.detect_idm_path()

                if self.main_window.shahid_downloader.is_idm_available():
                    idm_path = self.main_window.shahid_downloader.idm_path
                    self.idm_status_label.setText(f"✅ IDM found at: {idm_path}")
                    self.idm_status_label.setStyleSheet("color: #50fa7b; font-size: 13px;")
                else:
                    self.idm_status_label.setText("❌ IDM not found or not accessible")
                    self.idm_status_label.setStyleSheet("color: #ff5555; font-size: 13px;")
            else:
                self.idm_status_label.setText("⚠️ Downloader not initialized")
                self.idm_status_label.setStyleSheet("color: #ffb86c; font-size: 13px;")
        except Exception as e:
            self.idm_status_label.setText(f"❌ Error checking IDM: {str(e)}")
            self.idm_status_label.setStyleSheet("color: #ff5555; font-size: 13px;")

    def test_idm_connection(self):
        """Test IDM connection."""
        try:
            # Update IDM path first
            custom_path = self.idm_path_input.text().strip()
            if custom_path:
                self.main_window.shahid_downloader.idm_path = custom_path
            else:
                self.main_window.shahid_downloader.idm_path = self.main_window.shahid_downloader.detect_idm_path()

            if self.main_window.shahid_downloader.is_idm_available():
                # Try to run IDM with help parameter to test if it's working
                import subprocess
                try:
                    result = subprocess.run([self.main_window.shahid_downloader.idm_path],
                                          capture_output=True, timeout=5)
                    QMessageBox.information(
                        self.main_window,
                        "IDM Test",
                        "✅ IDM is working correctly!\n\n"
                        f"IDM Path: {self.main_window.shahid_downloader.idm_path}"
                    )
                except subprocess.TimeoutExpired:
                    # Timeout is expected as IDM might open its interface
                    QMessageBox.information(
                        self.main_window,
                        "IDM Test",
                        "✅ IDM is accessible and responding!\n\n"
                        f"IDM Path: {self.main_window.shahid_downloader.idm_path}"
                    )
                except Exception as e:
                    QMessageBox.warning(
                        self.main_window,
                        "IDM Test",
                        f"⚠️ IDM found but may not be working properly:\n{str(e)}\n\n"
                        f"IDM Path: {self.main_window.shahid_downloader.idm_path}"
                    )
            else:
                QMessageBox.warning(
                    self.main_window,
                    "IDM Test",
                    "❌ IDM not found or not accessible.\n\n"
                    "Please check the IDM path or install IDM from:\n"
                    "https://www.internetdownloadmanager.com/"
                )
        except Exception as e:
            QMessageBox.critical(
                self.main_window,
                "IDM Test Error",
                f"An error occurred while testing IDM:\n{str(e)}"
            )

        # Update status after test
        self.update_idm_status()
