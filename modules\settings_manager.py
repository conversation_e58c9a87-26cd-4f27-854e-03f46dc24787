import json
import os
from pathlib import Path
from PySide6.QtCore import QObject, Signal

class SettingsManager(QObject):
    # Signals
    settings_changed = Signal(dict)
    
    def __init__(self):
        super().__init__()
        # Point to YANGO root directory (2 levels up from modules/settings_manager.py)
        self.base_dir = Path(__file__).parent.parent
        self.config_dir = self.base_dir / "config"
        self.config_dir.mkdir(exist_ok=True)
        self.settings_file = self.config_dir / "yango_settings.json"
        
        # Default settings
        self.default_settings = {
            "download_path": str(self.base_dir / "downloads"),
            "cache_path": str(self.base_dir / "cache"),
            "keys_path": str(self.base_dir / "KEYS"),
            "binaries_path": str(self.base_dir / "binaries"),
            "default_quality": "720p",
            "default_audio": ["ar", "en"],
            "default_subtitles": ["ar", "en"],
            "concurrent_downloads": 3,
            "retry_count": 3,
            "auto_refresh_token": True,
            "token_refresh_interval": 1800,  # 30 minutes
            "theme": "dark",
            "language": "en",
            "auto_download": False,
            "notification_enabled": True,
            "log_level": "INFO"
        }
        
        self.settings = self.load_settings()
    
    def load_settings(self):
        """Load settings from file"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                
                # Merge with defaults to ensure all keys exist
                settings = self.default_settings.copy()
                settings.update(loaded_settings)
                return settings
            else:
                return self.default_settings.copy()
                
        except Exception as e:
            print(f"Error loading settings: {e}")
            return self.default_settings.copy()
    
    def save_settings(self):
        """Save settings to file"""
        try:
            # Create backup
            if self.settings_file.exists():
                backup_file = self.settings_file.with_suffix('.json.bak')
                self.settings_file.rename(backup_file)
            
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=4, ensure_ascii=False)
            
            self.settings_changed.emit(self.settings)
            return True
            
        except Exception as e:
            print(f"Error saving settings: {e}")
            return False
    
    def get_setting(self, key, default=None):
        """Get a specific setting"""
        return self.settings.get(key, default)
    
    def set_setting(self, key, value):
        """Set a specific setting"""
        self.settings[key] = value
        return self.save_settings()
    
    def get_all_settings(self):
        """Get all settings"""
        return self.settings.copy()
    
    def update_settings(self, new_settings):
        """Update multiple settings"""
        self.settings.update(new_settings)
        return self.save_settings()
    
    def reset_settings(self):
        """Reset to default settings"""
        self.settings = self.default_settings.copy()
        return self.save_settings()
    
    def get_download_path(self):
        """Get download path"""
        return Path(self.get_setting("download_path"))
    
    def set_download_path(self, path):
        """Set download path"""
        return self.set_setting("download_path", str(path))
    
    def get_cache_path(self):
        """Get cache path"""
        return Path(self.get_setting("cache_path"))
    
    def set_cache_path(self, path):
        """Set cache path"""
        return self.set_setting("cache_path", str(path))
    
    def get_keys_path(self):
        """Get keys path"""
        return Path(self.get_setting("keys_path"))
    
    def set_keys_path(self, path):
        """Set keys path"""
        return self.set_setting("keys_path", str(path))
    
    def get_binaries_path(self):
        """Get binaries path"""
        return Path(self.get_setting("binaries_path"))
    
    def set_binaries_path(self, path):
        """Set binaries path"""
        return self.set_setting("binaries_path", str(path))
    
    def get_default_quality(self):
        """Get default quality"""
        return self.get_setting("default_quality", "720p")
    
    def set_default_quality(self, quality):
        """Set default quality"""
        return self.set_setting("default_quality", quality)
    
    def get_default_audio(self):
        """Get default audio tracks"""
        return self.get_setting("default_audio", ["ar", "en"])
    
    def set_default_audio(self, audio_tracks):
        """Set default audio tracks"""
        return self.set_setting("default_audio", audio_tracks)
    
    def get_default_subtitles(self):
        """Get default subtitle tracks"""
        return self.get_setting("default_subtitles", ["ar", "en"])
    
    def set_default_subtitles(self, subtitle_tracks):
        """Set default subtitle tracks"""
        return self.set_setting("default_subtitles", subtitle_tracks)
    
    def get_concurrent_downloads(self):
        """Get concurrent downloads count"""
        return self.get_setting("concurrent_downloads", 3)
    
    def set_concurrent_downloads(self, count):
        """Set concurrent downloads count"""
        return self.set_setting("concurrent_downloads", count)
    
    def get_retry_count(self):
        """Get retry count"""
        return self.get_setting("retry_count", 3)
    
    def set_retry_count(self, count):
        """Set retry count"""
        return self.set_setting("retry_count", count)
    
    def is_auto_refresh_enabled(self):
        """Check if auto refresh is enabled"""
        return self.get_setting("auto_refresh_token", True)
    
    def set_auto_refresh_enabled(self, enabled):
        """Set auto refresh enabled"""
        return self.set_setting("auto_refresh_token", enabled)
    
    def get_token_refresh_interval(self):
        """Get token refresh interval"""
        return self.get_setting("token_refresh_interval", 1800)
    
    def set_token_refresh_interval(self, interval):
        """Set token refresh interval"""
        return self.set_setting("token_refresh_interval", interval)
    
    def get_theme(self):
        """Get theme"""
        return self.get_setting("theme", "dark")
    
    def set_theme(self, theme):
        """Set theme"""
        return self.set_setting("theme", theme)
    
    def get_language(self):
        """Get language"""
        return self.get_setting("language", "en")
    
    def set_language(self, language):
        """Set language"""
        return self.set_setting("language", language)
    
    def is_auto_download_enabled(self):
        """Check if auto download is enabled"""
        return self.get_setting("auto_download", False)
    
    def set_auto_download_enabled(self, enabled):
        """Set auto download enabled"""
        return self.set_setting("auto_download", enabled)
    
    def is_notification_enabled(self):
        """Check if notifications are enabled"""
        return self.get_setting("notification_enabled", True)
    
    def set_notification_enabled(self, enabled):
        """Set notifications enabled"""
        return self.set_setting("notification_enabled", enabled)
    
    def get_log_level(self):
        """Get log level"""
        return self.get_setting("log_level", "INFO")
    
    def set_log_level(self, level):
        """Set log level"""
        return self.set_setting("log_level", level)
