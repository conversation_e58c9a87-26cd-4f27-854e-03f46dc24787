"""
YANGO Downloader Module
Handles downloading and merging of video content from YANGO platform
"""

import os
import subprocess
import shutil
from pathlib import Path
from PySide6.QtCore import QThread, Signal
import requests


class YangoDownloader(QThread):
    """Download manager for YANGO content"""

    # Signals
    download_progress = Signal(int, str)  # progress, status
    download_completed = Signal(str, bool)  # file_path, success
    download_error = Signal(str)  # error_message
    fetch_episode_streams = Signal(str, dict)  # episode_content_id, download_item
    status_update = Signal(str)  # status message for UI
    video_progress = Signal(int)  # video progress percentage
    audio_progress = Signal(int)  # audio progress percentage
    subtitle_progress = Signal(int)  # subtitle progress percentage

    def __init__(self, base_dir=None):
        super().__init__()
        # Always use current working directory for exe compatibility
        self.base_dir = Path(os.getcwd())
        self.setup_paths()
        self.download_queue = []
        self.current_download = None
        self.processing_episode_streams = False
        self.current_process = None  # Track current download process
        self.is_stopping = False  # Flag to prevent restart after stop

        # Initialize settings
        self.settings_manager = None
        self.load_settings()

    def setup_paths(self):
        """Setup required paths and directories"""
        from modules.resource_utils import get_resource_path, get_binary_path

        # Use resource utils for exe compatibility
        self.cache_dir = Path(get_resource_path("cache"))
        self.downloads_dir = Path(get_resource_path("downloads"))
        self.keys_file = Path(get_resource_path("KEYS")) / "KEYS.txt"

        # Binary paths - use resource utils
        self.mkvmerge_path = Path(get_binary_path("mkvmerge.exe"))
        self.n_m3u8dl_path = Path(get_binary_path("N_m3u8DL-RE.exe"))
        self.aria2c_path = Path(get_binary_path("aria2c.exe"))
        self.mp4decrypt_path = Path(get_binary_path("mp4decrypt.exe"))
        self.ffmpeg_path = Path(get_binary_path("ffmpeg.exe"))

        # Create directories
        self.cache_dir.mkdir(exist_ok=True)
        self.downloads_dir.mkdir(exist_ok=True)
        self.keys_file.parent.mkdir(exist_ok=True)

        # Debug: Print paths
        print(f"🔧 Setup paths:")
        print(f"   📁 Base dir: {self.base_dir}")
        print(f"   📁 Cache dir: {self.cache_dir}")
        print(f"   📁 Downloads dir: {self.downloads_dir}")
        print(f"   🔑 Keys file: {self.keys_file}")
        print(f"   🛠️ N_m3u8DL-RE: {self.n_m3u8dl_path}")
        print(f"   🛠️ aria2c: {self.aria2c_path}")
        print(f"   🛠️ mkvmerge: {self.mkvmerge_path}")
        print(f"   🛠️ mp4decrypt: {self.mp4decrypt_path}")
        print(f"   🛠️ ffmpeg: {self.ffmpeg_path}")

        # Check if tools exist
        print(f"🔍 Checking tools:")
        print(f"   N_m3u8DL-RE exists: {self.n_m3u8dl_path.exists()}")
        print(f"   aria2c exists: {self.aria2c_path.exists()}")
        print(f"   mkvmerge exists: {self.mkvmerge_path.exists()}")
        print(f"   mp4decrypt exists: {self.mp4decrypt_path.exists()}")
        print(f"   Keys file exists: {self.keys_file.exists()}")

    def add_download(self, download_item):
        """Add download item to queue"""
        self.download_queue.append(download_item)
        print(f"📥 Added to download queue: {download_item.get('title', 'Unknown')}")
        print(f"   🔗 MPD URL: {download_item.get('mpd_url', 'None')[:100]}...")
        print(f"   📺 Quality: {download_item.get('quality', 'None')}")
        print(f"   🔊 Audio: {download_item.get('audio_tracks', 'None')}")
        print(f"   📝 Subtitles: {download_item.get('subtitle_tracks', 'None')}")

    def start_downloads(self):
        """Start processing download queue"""
        if not self.download_queue:
            print("📭 No downloads in queue")
            return

        print(f"🚀 Starting downloads: {len(self.download_queue)} items")
        self.start()

    def continue_downloads(self):
        """Continue processing download queue after stream fetching"""
        if not self.download_queue:
            print("📭 No more downloads in queue")
            return

        if self.isRunning():
            print("⚠️ Downloader already running")
            return

        print(f"🔄 Continuing downloads: {len(self.download_queue)} items remaining")
        self.start()

    def run(self):
        """Main download thread"""
        try:
            while self.download_queue and not self.is_stopping:
                self.current_download = self.download_queue.pop(0)

                # Check if stopping before starting download
                if self.is_stopping:
                    print("🛑 Download stopped by user")
                    break

                # Check if we need to fetch episode streams
                if (not self.current_download.get('mpd_url') and
                    self.current_download.get('episode_content_id') and
                    not self.processing_episode_streams):

                    # Put the item back at the front of the queue
                    self.download_queue.insert(0, self.current_download)

                    # Process this download (which will trigger stream fetching)
                    self.process_download(self.current_download)

                    # Exit the loop - the UI will restart the downloader after fetching streams
                    break
                else:
                    # Process normal download
                    self.process_download(self.current_download)

        except Exception as e:
            print(f"❌ Download thread error: {str(e)}")
            self.download_error.emit(str(e))

    def process_download(self, download_item):
        """Process single download item - fetch episode data if needed"""
        try:
            title = download_item.get('title', 'Unknown')
            season = download_item.get('season', 'S1')
            episode = download_item.get('episode', 'E1')
            quality = download_item.get('quality', '720p')
            mpd_url = download_item.get('mpd_url', '')
            audio_tracks = download_item.get('audio_tracks', [])
            subtitle_tracks = download_item.get('subtitle_tracks', [])
            episode_data = download_item.get('episode_data', {})
            episode_content_id = download_item.get('episode_content_id', '')

            print(f"🎬 Processing: {title} - {season}{episode} - {quality}")
            print(f"   🔊 Audio tracks received: {audio_tracks}")
            print(f"   📝 Subtitle tracks received: {subtitle_tracks}")

            # If no MPD URL, we need to fetch episode streams first
            if not mpd_url and episode_content_id:
                print(f"🔄 No MPD URL found, fetching streams for episode content ID: {episode_content_id}")

                # Check if we're already processing episode streams
                if self.processing_episode_streams:
                    print("⏳ Already processing episode streams, waiting...")
                    return

                # Mark as processing episode streams
                self.processing_episode_streams = True

                # Signal to UI to fetch episode streams
                self.fetch_episode_streams.emit(episode_content_id, download_item)
                return  # Wait for streams to be fetched

            if not mpd_url:
                print(f"❌ No MPD URL available for {title} - {season}{episode}")
                self.download_error.emit(f"No MPD URL available for {title} - {season}{episode}")
                return

            # Create safe filename with proper formatting
            safe_title = self.sanitize_filename(title)

            # Format quality properly for filename
            quality_formatted = self.format_quality_for_filename(quality)

            # Check if this is a movie or series based on content_type or empty season/episode
            content_type = download_item.get('content_type', 'series')  # Default to series for backward compatibility
            is_movie = (content_type == 'movie' or
                       (not season or season == '' or season == 'S00') and
                       (not episode or episode == '' or episode == 'E00'))

            if is_movie:
                # Get movie filename format from settings
                filename_format = self.get_filename_format("movie")
                filename = filename_format.format(
                    title=safe_title,
                    quality=quality_formatted
                )

                # Setup directories for movies - direct folder without "Movies" parent
                movie_dir = self.downloads_dir / safe_title
                movie_dir.mkdir(parents=True, exist_ok=True)

                cache_movie_dir = self.cache_dir / f"{safe_title}_movie"
                cache_movie_dir.mkdir(parents=True, exist_ok=True)

                output_file = movie_dir / f"{filename}.mkv"
                cache_episode_dir = cache_movie_dir  # Use movie cache dir

                print(f"🎬 Movie download: {filename}")
                print(f"📁 Movie directory: {movie_dir}")

            else:
                # Series filename format: Title.S01.E01.720p.YANGO.WEB-DL.H264.AAC
                # Format season and episode numbers (S01, E01)
                if season.startswith('S'):
                    season_formatted = season
                else:
                    season_formatted = f"S{season.zfill(2)}"

                if episode.startswith('E'):
                    episode_formatted = episode
                else:
                    episode_formatted = f"E{episode.zfill(2)}"

                # Get series filename format from settings
                filename_format = self.get_filename_format("series")
                filename = filename_format.format(
                    title=safe_title,
                    season=season_formatted,
                    episode=episode_formatted,
                    quality=quality_formatted
                )

                # Setup directories for series
                series_dir = self.downloads_dir / safe_title / season_formatted
                series_dir.mkdir(parents=True, exist_ok=True)

                cache_episode_dir = self.cache_dir / f"{safe_title}_{season_formatted}{episode_formatted}"
                cache_episode_dir.mkdir(parents=True, exist_ok=True)

                output_file = series_dir / f"{filename}.mkv"

                print(f"📺 Series download: {filename}")

            # Check if already exists
            if output_file.exists():
                print(f"✅ File already exists: {output_file}")
                self.download_completed.emit(str(output_file), True)
                return

            self.download_progress.emit(0, f"Starting download: {filename}")

            # Download using N_m3u8DL-RE
            original_resolution = download_item.get('original_resolution', '')
            success = self.download_with_n_m3u8dl(
                mpd_url, filename, cache_episode_dir,
                quality, audio_tracks, subtitle_tracks, original_resolution
            )

            if success:
                # Find downloaded files
                video_files = list(cache_episode_dir.glob(f"{filename}*.mp4"))
                audio_files = list(cache_episode_dir.glob(f"{filename}*.m4a"))

                if video_files:
                    self.download_progress.emit(90, "Merging files...")

                    # Find subtitle files
                    subtitle_files = list(cache_episode_dir.glob(f"{filename}*.vtt")) + \
                                   list(cache_episode_dir.glob(f"{filename}*.srt"))

                    print(f"📝 Found subtitle files: {[f.name for f in subtitle_files]}")

                    # Merge with mkvmerge (including subtitles)
                    self.merge_with_mkvmerge(
                        video_files[0], audio_files, subtitle_files, output_file
                    )

                    # Clean up cache
                    try:
                        shutil.rmtree(cache_episode_dir)
                        print(f"🧹 Cleaned cache: {cache_episode_dir}")
                    except Exception as e:
                        print(f"⚠️ Cache cleanup error: {e}")

                    self.download_progress.emit(100, "✅ Completed!")
                    self.download_completed.emit(str(output_file), True)
                else:
                    self.download_error.emit("No video files found after download")
            else:
                self.download_error.emit("Download failed")

        except Exception as e:
            print(f"❌ Download processing error: {str(e)}")
            self.download_error.emit(str(e))

    def build_subtitle_selection(self, subtitle_tracks):
        """Build subtitle selection string based on user choice"""
        try:
            if not subtitle_tracks:
                print("   📝 No subtitles selected")
                return None  # Return None to use --no-subtitle flag

            # Extract language codes from subtitle tracks
            selected_languages = []
            for subtitle in subtitle_tracks:
                if isinstance(subtitle, dict):
                    language = subtitle.get('language', '').lower()
                elif isinstance(subtitle, str):
                    # Parse language from string like "ENGLISH" or "ARABIC"
                    language = subtitle.lower()
                    if 'english' in language or 'eng' in language:
                        language = 'eng'
                    elif 'arabic' in language or 'ara' in language:
                        language = 'ara'
                    elif 'french' in language or 'fra' in language:
                        language = 'fra'
                else:
                    continue

                # Map language names to codes (handle both full names and codes)
                if language in ['english', 'eng']:
                    selected_languages.append('eng')
                elif language in ['arabic', 'ara']:
                    selected_languages.append('ara')
                elif language in ['french', 'fra']:
                    selected_languages.append('fra')
                else:
                    # Try to use the language as-is if it's a valid 3-letter code
                    if len(language) == 3 and language.isalpha():
                        selected_languages.append(language)

            if not selected_languages:
                print("   📝 No valid subtitle languages found, using none")
                return None  # Return None to use --no-subtitle flag

            # Remove duplicates and build selection string
            unique_languages = list(set(selected_languages))
            language_string = "|".join(unique_languages)

            print(f"   📝 Selected subtitle languages: {unique_languages}")
            # Use :for=all to download all selected languages, not just the best one
            return f"lang={language_string}:for=all"

        except Exception as e:
            print(f"❌ Error building subtitle selection: {str(e)}")
            return None  # Fallback to no subtitles instead of default

    def build_audio_selection(self, audio_tracks):
        """Build audio selection string based on user choice with precise track selection"""
        try:
            if not audio_tracks:
                print("   🔊 No audio selected, using stereo-preferred default")
                # Prefer stereo (2CH) over surround when no specific choice is made
                # Use :for=worst to get lower bitrate (usually stereo)
                return "lang=ar|en|tr:for=worst"  # Default to stereo (lower bitrate)

            print(f"   🔊 Processing {len(audio_tracks)} selected audio tracks:")

            # Extract language codes from audio tracks with detailed parsing
            selected_languages = []
            track_details = []

            for i, audio in enumerate(audio_tracks):
                print(f"      [{i+1}] Processing: {audio}")

                if isinstance(audio, dict):
                    language = audio.get('language', '').lower()
                elif isinstance(audio, str):
                    # Parse language from string like "Arabic - Stereo (2 channels)" or "Arabic - 5.1 Surround (6 channels)"
                    language = audio.lower()
                    track_details.append(audio)

                    # Extract language with better parsing
                    if 'arabic' in language or 'ara' in language:
                        language = 'ara'
                    elif 'english' in language or 'eng' in language:
                        language = 'eng'
                    elif 'french' in language or 'fra' in language:
                        language = 'fra'
                    elif 'turkish' in language or 'tur' in language:
                        language = 'tur'
                    else:
                        # Try to extract language from the beginning of the string
                        first_word = audio.split(' ')[0].lower()
                        if first_word in ['arabic', 'english', 'french', 'turkish']:
                            language = {'arabic': 'ara', 'english': 'eng', 'french': 'fra', 'turkish': 'tur'}[first_word]
                        else:
                            print(f"      ⚠️ Unknown language in: {audio}")
                            continue
                else:
                    print(f"      ⚠️ Unsupported audio format: {type(audio)}")
                    continue

                # Map language names to codes
                if language in ['arabic', 'ara']:
                    selected_languages.append('ara')
                elif language in ['english', 'eng']:
                    selected_languages.append('eng')
                elif language in ['french', 'fra']:
                    selected_languages.append('fra')
                elif language in ['turkish', 'tur']:
                    selected_languages.append('tur')
                else:
                    # Try to use the language as-is if it's a valid 3-letter code
                    if len(language) == 3 and language.isalpha():
                        selected_languages.append(language)
                        print(f"      ✅ Using language code: {language}")

            if not selected_languages:
                print("   🔊 No valid audio languages found, using stereo-preferred default")
                return "lang=ar|en|tr:for=worst"  # Prefer stereo (lower bitrate)

            # Remove duplicates and build selection string
            unique_languages = list(set(selected_languages))
            language_string = "|".join(unique_languages)

            print(f"   🔊 Final selected audio languages: {unique_languages}")
            print(f"   🔊 Track details: {track_details}")

            # Analyze track details to determine preferred audio type
            prefer_stereo = False
            prefer_surround = False

            for track in track_details:
                if 'stereo' in track.lower() or '2 channels' in track.lower():
                    prefer_stereo = True
                    print(f"   🔊 Detected stereo preference from: {track}")
                elif 'surround' in track.lower() or '5.1' in track.lower() or '6 channels' in track.lower():
                    prefer_surround = True
                    print(f"   🔊 Detected surround preference from: {track}")

            # Build more specific selection based on user choice
            if prefer_stereo and not prefer_surround:
                # User specifically selected stereo - try multiple approaches
                # First try channel count, then codec, then worst quality (stereo is usually lower bitrate)
                selection = f"lang={language_string}:for=worst"
                print(f"   🔊 Using stereo-preferred selection (worst=stereo): {selection}")
                return selection
            elif prefer_surround and not prefer_stereo:
                # User specifically selected surround - use best quality (surround is usually higher bitrate)
                selection = f"lang={language_string}:for=best"
                print(f"   🔊 Using surround-preferred selection (best=surround): {selection}")
                return selection
            else:
                # Mixed or unclear preference - prefer stereo by using worst (lower bitrate = stereo)
                selection = f"lang={language_string}:for=worst"
                print(f"   🔊 Using stereo-default selection (worst=stereo): {selection}")
                return selection

        except Exception as e:
            print(f"❌ Error building audio selection: {str(e)}")
            return "lang=ar|en|tr:for=worst"  # Fallback to stereo (lower bitrate)

    def process_output_line(self, line_content, progress_tracker, output_lines):
        """Process a single output line from N_m3u8DL-RE - NEVER allows 100%"""
        import re

        output_lines.append(line_content)

        if not line_content:
            return progress_tracker

        # STRICT RULE: Never allow progress to reach 100% from parsing
        # Only actual file completion can trigger 100%

        # Emit status updates for non-progress lines (silent mode)
        if not (line_content.startswith("Vid ") and "%" in line_content) and \
           not (line_content.startswith("Aud ") and "%" in line_content) and \
           not (line_content.startswith("Sub ") and "%" in line_content):
            self.status_update.emit(f"N_m3u8DL: {line_content}")

        # Enhanced progress tracking with multiple patterns to catch all formats

        # Initialize component tracking if not exists
        if 'components' not in progress_tracker:
            progress_tracker['components'] = {
                'video': {'progress': 0, 'completed': False, 'active': False},
                'audio': {'progress': 0, 'completed': False, 'active': False},
                'subtitle': {'progress': 0, 'completed': False, 'active': False}
            }

        # Debug: Print any line that might contain progress info
        if "%" in line_content or "MB/" in line_content:
            print(f"🔍 DEBUG Progress Line: {line_content}")

        # Enhanced progress tracking with proper phase management
        if "%" in line_content and not ("Binary merging" in line_content):
            percentage_match = re.search(r"(\d+)%", line_content)
            if percentage_match:
                prog = int(percentage_match.group(1))

                # Determine current phase and component
                current_phase = progress_tracker.get('current_phase', 'starting')

                # Phase 1: Subtitle download - STATUS ONLY, NO PROGRESS BAR MOVEMENT
                if "Sub" in line_content:
                    if current_phase != 'subtitles':
                        progress_tracker['current_phase'] = 'subtitles'
                        # Reset progress to 0 when starting subtitles
                        progress_tracker['last_overall'] = 0
                        print(f"🔄 Phase 1: Starting subtitle download - Reset progress to 0%")

                    # Update status message but keep progress bar at 0 for subtitles
                    message = f"Downloading subtitles... {prog}%"
                    progress_tracker['components']['subtitle']['progress'] = prog
                    progress_tracker['components']['subtitle']['active'] = True

                    # Keep progress at 0 during subtitle download
                    self.download_progress.emit(0, message)
                    print(f"📝 ✅ SUBTITLE STATUS: {prog}% | Status: {message} | Progress bar: 0%")

                    return progress_tracker

                # Phase 2: Video and Audio download - PROGRESS BAR MOVES
                elif "Vid" in line_content or "Aud" in line_content:
                    if current_phase == 'subtitles':
                        # Transition from subtitles to main download
                        progress_tracker['current_phase'] = 'main_download'
                        print(f"🔄 Phase 2: Starting video/audio download")
                    elif current_phase != 'main_download':
                        progress_tracker['current_phase'] = 'main_download'
                        print(f"🔄 Phase 2: Starting main download (no subtitles)")

                    # Track both video and audio components
                    if "Vid" in line_content:
                        progress_tracker['components']['video']['progress'] = prog
                        progress_tracker['components']['video']['active'] = True
                        component_type = "video"
                    else:  # Audio
                        progress_tracker['components']['audio']['progress'] = prog
                        progress_tracker['components']['audio']['active'] = True
                        component_type = "audio"

                    # Calculate unified progress based on active components
                    video_prog = progress_tracker['components']['video']['progress']
                    audio_prog = progress_tracker['components']['audio']['progress']
                    video_active = progress_tracker['components']['video']['active']
                    audio_active = progress_tracker['components']['audio']['active']

                    # Use MINIMUM progress to ensure both components finish before reaching 90%
                    if video_active and audio_active:
                        # Both active - use minimum progress (slowest component)
                        unified_prog = min(video_prog, audio_prog)
                        message = f"Downloading video & audio... V:{video_prog}% A:{audio_prog}%"
                    elif video_active:
                        unified_prog = video_prog
                        message = f"Downloading video... {video_prog}%"
                    elif audio_active:
                        unified_prog = audio_prog
                        message = f"Downloading audio... {audio_prog}%"
                    else:
                        unified_prog = prog
                        message = f"Downloading... {prog}%"

                    # Scale to 90% max (reserve 10% for merging)
                    final_progress = min(int(unified_prog * 0.9), 90)
                    progress_tracker['last_overall'] = final_progress

                    self.download_progress.emit(final_progress, message)
                    print(f"🎬🔊 ✅ DOWNLOAD PROGRESS: {component_type} {prog}% -> unified {final_progress}% | V:{video_prog}% A:{audio_prog}%")

                    return progress_tracker

                else:
                    # General progress - keep current progress
                    current_progress = progress_tracker.get('last_overall', 0)
                    message = f"Processing... {prog}%"
                    self.download_progress.emit(current_progress, message)
                    print(f"📊 ✅ GENERAL STATUS: {message} | Progress bar stays at: {current_progress}%")

                    return progress_tracker

        # Legacy patterns are now handled by the enhanced phase management above
        # This section is kept for compatibility but should not be reached
        video_progress_found = False

        # Legacy audio and subtitle patterns - now handled by enhanced phase management above
        # These sections are kept for compatibility but should not be reached due to early return

        # Phase 3: Merging and final processing - PROGRESS BAR MOVES 90-100%
        # Only trigger merging phase when we have actual merging activity (not just warnings)
        if ("Merging" in line_content or "Decrypting" in line_content) and "automatically enabled" not in line_content:
            current_phase = progress_tracker.get('current_phase', 'unknown')
            current_progress = progress_tracker.get('last_overall', 0)

            # Only start merging phase if we've completed video/audio download (progress >= 80%)
            if current_progress >= 80:
                if current_phase != 'merging':
                    progress_tracker['current_phase'] = 'merging'
                    print(f"🔄 Phase 3: Starting merging/decryption at {current_progress}%")

                # Merging phase: continue from current progress to 100%
                if "Merging" in line_content and "Binary" not in line_content:
                    merge_progress = max(current_progress, 95)
                    message = "Merging files..."
                elif "Decrypting" in line_content:
                    merge_progress = max(current_progress, 98)
                    message = "Decrypting files..."
                else:
                    merge_progress = max(current_progress, 92)
                    message = "Processing files..."

                progress_tracker['last_overall'] = merge_progress
                self.download_progress.emit(merge_progress, message)
                print(f"🔧 ✅ MERGING PROGRESS: {merge_progress}% | {message}")
            else:
                # Still in download phase - ignore merging messages
                print(f"🔧 Ignoring early merging message at {current_progress}%: {line_content}")

        # Ignore "Binary merging is automatically enabled" message - it's just a warning at start
        elif "Binary merging" in line_content:
            # This is just a warning/info message, not actual merging - ignore it
            print(f"🔧 Ignoring Binary merging info message: {line_content}")
            pass

        # Phase 4: Completion detection - only when all components are truly finished
        elif "Done" in line_content and len(line_content.strip()) < 20:  # Simple "Done" message
            # Check if we're really done with all components
            video_prog = progress_tracker['components']['video']['progress']
            audio_prog = progress_tracker['components']['audio']['progress']
            video_active = progress_tracker['components']['video']['active']
            audio_active = progress_tracker['components']['audio']['active']

            # Only complete if both components reached 100% or are inactive
            if (not video_active or video_prog >= 100) and (not audio_active or audio_prog >= 100):
                progress_tracker['current_phase'] = 'completed'
                progress_tracker['last_overall'] = 100
                self.download_progress.emit(100, "Download completed!")
                print(f"🎉 ✅ COMPLETION: 100% - All components finished | V:{video_prog}% A:{audio_prog}%")
            else:
                # Still downloading - don't complete yet
                current_progress = progress_tracker.get('last_overall', 0)
                self.download_progress.emit(current_progress, f"Finishing download... V:{video_prog}% A:{audio_prog}%")
                print(f"🔄 Still downloading: V:{video_prog}% A:{audio_prog}% - Not completing yet")

        elif "Processing" in line_content and ("subtitle" in line_content.lower() or "sub" in line_content.lower()):
            current_progress = max(progress_tracker.get('last_overall', 0), progress_tracker.get('last_progress', 0))
            # Only add small increment for subtitle processing, and only if we're near completion
            if current_progress >= 90:
                overall_progress = min(current_progress + 1, 99)  # Very small increment only
                if overall_progress > progress_tracker.get('last_overall', 0):
                    progress_tracker['last_overall'] = overall_progress
                    self.download_progress.emit(overall_progress, "Processing subtitles...")
                    print(f"📊 Processing Subs | Overall: {overall_progress}%")

        # Enhanced fallback: Try to catch any percentage in the line (for debugging)
        elif not video_progress_found and "%" in line_content and not ("Binary merging" in line_content):
            # Enhanced pattern matching for any progress line
            percentage_match = re.search(r"(\d+)%", line_content)
            if percentage_match:
                prog = int(percentage_match.group(1))
                current_overall = progress_tracker.get('last_overall', 0)
                if prog > current_overall and prog <= 100:
                    print(f"🎯 FOUND PROGRESS: {prog}% in line: {line_content}")
                    # Try to determine which component this might be and track it
                    if "Vid" in line_content or "video" in line_content.lower():
                        progress_tracker['components']['video']['progress'] = prog
                        progress_tracker['components']['video']['active'] = True
                    elif "Aud" in line_content or "audio" in line_content.lower():
                        progress_tracker['components']['audio']['progress'] = prog
                        progress_tracker['components']['audio']['active'] = True
                    elif "Sub" in line_content or "subtitle" in line_content.lower():
                        progress_tracker['components']['subtitle']['progress'] = prog
                        progress_tracker['components']['subtitle']['active'] = True

                    # Allow full progress range including 100%
                    capped_prog = prog  # Allow full progress including 100%
                    progress_tracker['last_overall'] = capped_prog
                    self.download_progress.emit(capped_prog, f"Progress... {capped_prog}%")
                    print(f"📊 ✅ PROGRESS UPDATED: {prog}% -> {capped_prog}% from line: {line_content[:50]}...")

        # Progress tracking now allows full range including 100% for real-time updates

        return progress_tracker

    def calculate_unified_progress(self, progress_tracker):
        """Calculate unified progress that continues until all active components are completed"""
        components = progress_tracker.get('components', {})

        # Find active components
        active_components = []
        for comp_name, comp_data in components.items():
            if comp_data.get('active', False):
                active_components.append(comp_name)

        if not active_components:
            return progress_tracker.get('last_overall', 0)

        # Calculate weighted progress based on active components
        total_progress = 0
        total_weight = 0

        for comp_name in active_components:
            comp_data = components[comp_name]
            comp_progress = comp_data.get('progress', 0)

            # Weight: Video 60%, Audio 30%, Subtitle 10%
            if comp_name == 'video':
                weight = 0.6
            elif comp_name == 'audio':
                weight = 0.3
            else:  # subtitle
                weight = 0.1

            total_progress += comp_progress * weight
            total_weight += weight

        if total_weight == 0:
            return 0

        # Calculate average progress
        unified_progress = int(total_progress / total_weight)

        # Check if all active components are completed
        all_completed = all(components[comp].get('completed', False) for comp in active_components)

        if all_completed:
            # All active components completed - allow reaching 100%
            unified_progress = 100
            print(f"🎉 All active components completed: {active_components}")
        else:
            # Keep some room for final processing
            unified_progress = min(unified_progress, 98)

        print(f"📊 Unified Progress: {unified_progress}% | Active: {active_components} | Completed: {[comp for comp in active_components if components[comp].get('completed', False)]}")

        return unified_progress

    def calculate_overall_progress(self, tracker):
        """Simple progress calculation - deprecated, using simplified tracking instead"""
        # This method is kept for compatibility but not used in the new simplified system
        return tracker.get('last_overall', tracker.get('last_progress', 0))

    def calculate_simple_overall_progress(self, current_progress):
        """Calculate simple overall progress based on Vid, Aud, Sub progress"""
        vid_progress = current_progress.get('Vid', 0)
        aud_progress = current_progress.get('Aud', 0)
        sub_progress = current_progress.get('Sub', 0)

        # Simple weighted average: Video 60%, Audio 30%, Subtitle 10%
        # Only count components that have started (> 0)
        total_weight = 0
        weighted_sum = 0

        if vid_progress > 0:
            weighted_sum += vid_progress * 0.6
            total_weight += 0.6
        if aud_progress > 0:
            weighted_sum += aud_progress * 0.3
            total_weight += 0.3
        if sub_progress > 0:
            weighted_sum += sub_progress * 0.1
            total_weight += 0.1

        if total_weight == 0:
            return 0

        # Normalize to 85% (reserve 15% for post-processing)
        overall = (weighted_sum / total_weight) * 0.85
        return min(int(overall), 85)

    def download_with_n_m3u8dl(self, mpd_url, filename, cache_dir, quality, audio_tracks, subtitle_tracks, original_resolution=''):
        """Download using N_m3u8DL-RE"""
        try:
            # Reset progress tracker for smooth progression
            if hasattr(self, '_last_overall_progress'):
                delattr(self, '_last_overall_progress')

            print(f"🎬 Starting download with N_m3u8DL-RE")
            print(f"   📁 Cache dir: {cache_dir}")
            print(f"   📄 Filename: {filename}")
            print(f"   🔗 MPD URL: {mpd_url[:100]}...")

            # Check if binary exists
            if not self.n_m3u8dl_path.exists():
                print(f"❌ N_m3u8DL-RE not found at: {self.n_m3u8dl_path}")
                return False

            if not self.keys_file.exists():
                print(f"❌ Keys file not found at: {self.keys_file}")
                return False

            # Handle quality selection (following original YANGO script pattern)
            if 'x' in quality:
                # Quality is already in format like "1920x804" - use directly
                resolution_selector = f"res={quality}"
                print(f"   📺 Using exact resolution: {quality}")
            else:
                # Map display quality to actual resolution values (based on real stream analysis)
                quality_mapping = {
                    '1080p': '804',       # 1920x804 (actual YANGO height)
                    '960p': '960',        # 1920x960 (new format)
                    '720p': '536',        # 1280x536 (actual YANGO height)
                    '640p': '640',        # 1280x640 (new format)
                    '576p': '576',        # 1024x576
                    '520p': '512',        # 1024x512 (new format)
                    '512p': '512',        # 1024x512
                    '480p': '480',        # 854x480
                    '430p': '430',        # 1024x430 (actual YANGO height)
                    '428p': '428',        # 854x428
                    '360p': '358',        # 854x358 (actual YANGO height)
                    '320p': '320',        # 640x320
                    '260p': '268',        # 640x268 (actual YANGO height)
                    '2160p': '1608',      # 3840x1608 (actual YANGO 4K height)
                    '1440p': '1072',      # 2560x1072 (actual YANGO 2K height)
                }

                # Extract resolution from quality
                if quality in quality_mapping:
                    # Use mapped resolution for standard qualities
                    mapped_resolution = quality_mapping[quality]
                    # Handle multiple resolution options (e.g., "960|812")
                    if '|' in mapped_resolution:
                        # Use the first option as primary
                        resolution = mapped_resolution.split('|')[0]
                        print(f"   🔄 Mapped {quality} -> {resolution} (primary from {mapped_resolution})")
                    else:
                        resolution = mapped_resolution
                        print(f"   🔄 Mapped {quality} -> {resolution}")
                elif 'p' in quality:
                    # Handle format like "720p" - try to map or use as-is
                    base_quality = quality
                    if base_quality in quality_mapping:
                        mapped_resolution = quality_mapping[base_quality]
                        if '|' in mapped_resolution:
                            resolution = mapped_resolution.split('|')[0]
                            print(f"   🔄 Mapped {base_quality} -> {resolution} (primary from {mapped_resolution})")
                        else:
                            resolution = mapped_resolution
                            print(f"   🔄 Mapped {base_quality} -> {resolution}")
                    else:
                        resolution = quality.replace('p', '')
                else:
                    # Default fallback
                    resolution = '960'  # Default to 720p equivalent
                    print(f"   ⚠️ Unknown quality format '{quality}', using default: {resolution}")

                # Check if we have original resolution parameter
                if original_resolution and 'x' in original_resolution:
                    # Use original resolution for precise matching
                    resolution_selector = f"res={original_resolution}"
                    print(f"   📺 Target resolution: {original_resolution} (original resolution)")
                elif 'x' in quality:
                    # Quality is already in format like "640x268"
                    resolution_selector = f"res={quality}"
                    print(f"   📺 Target resolution: {quality} (full resolution)")
                else:
                    # Fallback to height-based selection
                    resolution_selector = f"height={resolution}"
                    print(f"   📺 Target resolution: {resolution} (height-based)")

            # Build audio and subtitle selection based on user choice
            print(f"   🔊 Audio tracks received: {audio_tracks}")
            print(f"   📝 Subtitle tracks received: {subtitle_tracks}")
            audio_selection = self.build_audio_selection(audio_tracks)
            subtitle_selection = self.build_subtitle_selection(subtitle_tracks)
            print(f"   🔊 Audio selection: {audio_selection}")
            print(f"   📝 Subtitle selection: {subtitle_selection}")

            # Build download command with proper escaping (following original YANGO script)
            download_command = [
                str(self.n_m3u8dl_path),
                mpd_url,
                "--select-video", resolution_selector,  # Use exact resolution or height
                "--select-audio", audio_selection,  # Use user-selected audio
            ]

            # Add subtitle selection or drop-subtitle flag
            if subtitle_selection is None:
                download_command.extend(["--drop-subtitle", ".*"])
                print(f"   📝 Subtitle selection: disabled (--drop-subtitle)")
            else:
                download_command.extend(["--select-subtitle", subtitle_selection])
                print(f"   📝 Subtitle selection: {subtitle_selection}")

            # Add remaining parameters with concurrent download settings
            download_command.extend([
                "--tmp-dir", str(cache_dir),
                "--save-dir", str(cache_dir),
                "--save-name", filename,
                "--decryption-binary-path", str(self.mp4decrypt_path),
                "--key-text-file", str(self.keys_file),
                "--log-level", "INFO",
                "--live-fix-vtt-by-audio",  # Additional flag for better progress reporting
                "--concurrent-download",  # Enable concurrent downloading
                "--download-retry-count", "3",  # Retry failed downloads
                "--binary-merge"  # Enable binary merging for better performance
            ])

            print(f"🚀 Download command:")
            for i, arg in enumerate(download_command):
                print(f"   [{i}] {arg}")

            # Reset progress to 0% at start of new download
            self.download_progress.emit(0, "Starting download process...")
            print(f"🔄 NEW DOWNLOAD: Reset progress to 0%")

            # Set environment for better output handling
            env = os.environ.copy()
            env['PYTHONUNBUFFERED'] = '1'
            env['TERM'] = 'xterm'

            process = subprocess.Popen(
                download_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Merge stderr with stdout for simpler handling
                text=True,
                universal_newlines=True,
                bufsize=0,  # Unbuffered for real-time output
                env=env,
                cwd=str(cache_dir),
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            # Store current process for stopping capability
            self.current_process = process

            # Real-time Progress Tracking with unbuffered reading
            import threading
            import queue

            output_lines = []
            output_queue = queue.Queue()

            # Initialize progress tracker for this download
            progress_tracker = {
                'current_phase': 'starting',
                'last_overall': 0,
                'components': {
                    'subtitle': {'progress': 0, 'active': False},
                    'video': {'progress': 0, 'active': False},
                    'audio': {'progress': 0, 'active': False}
                }
            }

            # Initialize progress at 0%
            self.download_progress.emit(0, "Initializing download...")
            print("📊 Initializing download at 0%...")

            def read_output():
                """Read output in a separate thread for real-time processing"""
                try:
                    while True:
                        line = process.stdout.readline()
                        if not line:
                            break
                        line_content = line.strip()
                        if line_content:  # Only queue non-empty lines
                            output_queue.put(line_content)
                except Exception as e:
                    print(f"❌ Error reading output: {e}")
                finally:
                    output_queue.put(None)  # Signal end of output

            # Start output reading thread
            output_thread = threading.Thread(target=read_output, daemon=True)
            output_thread.start()

            # Process output in real-time with faster response
            while True:
                try:
                    # Check if stopping
                    if self.is_stopping:
                        print("🛑 Stopping download process...")
                        if process.poll() is None:  # Process still running
                            process.terminate()
                            try:
                                process.wait(timeout=3)
                            except subprocess.TimeoutExpired:
                                process.kill()
                        break

                    # Get line with shorter timeout for better responsiveness
                    line_content = output_queue.get(timeout=0.5)

                    if line_content is None:  # End of output signal
                        break

                    # Print all lines for debugging
                    print(f"   📤 {line_content}")

                    # Use the unified progress tracking system
                    progress_tracker = self.process_output_line(line_content, progress_tracker, output_lines)

                except queue.Empty:
                    # Check if process is still running
                    if process.poll() is not None:
                        # Process finished, drain remaining output
                        while not output_queue.empty():
                            try:
                                line_content = output_queue.get_nowait()
                                if line_content and line_content is not None:
                                    print(f"   📤 {line_content}")
                                    progress_tracker = self.process_output_line(line_content, progress_tracker, output_lines)
                            except queue.Empty:
                                break

                        # Process has ended - check final progress
                        final_progress = progress_tracker.get('last_overall', 0)
                        print(f"🏁 Process ended with final progress: {final_progress}%")

                        # Keep progress at current level - don't increase until actual completion check

                        break
                    # Continue waiting for more output
                    continue
                except Exception as e:
                    print(f"❌ Error processing output: {e}")
                    break

            # تأكد من انتهاء العملية
            process.wait()

            return_code = process.returncode
            print(f"   🏁 Process finished with return code: {return_code}")

            if return_code == 0:
                print("✅ Download process completed successfully")

                # Check if files were actually downloaded before setting to 100%
                video_files = list(cache_dir.glob(f"{filename}*.mp4"))
                if video_files:
                    # Files exist - download truly completed
                    self.download_progress.emit(100, "Download completed!")
                    print(f"✅ Download completed successfully - Progress: 100%")
                else:
                    # Process succeeded but no files - keep current progress
                    current_progress = progress_tracker.get('last_overall', 0)
                    self.download_progress.emit(current_progress, "Processing...")
                    print(f"⚠️ Process completed but checking files... Progress: {current_progress}%")

                if not video_files:
                    print("⚠️ No video files found, trying alternative video selection...")

                    # Try with fallback selectors based on original quality
                    # Extract height from original quality for better fallback
                    original_height = None
                    if 'x' in quality:
                        try:
                            original_height = int(quality.split('x')[1])
                        except:
                            pass
                    elif quality.endswith('p'):
                        try:
                            original_height = int(quality[:-1])
                        except:
                            pass

                    # Create fallback selectors that respect the original quality choice
                    if original_height:
                        # Use exact height first, then lower qualities
                        fallback_selectors = [
                            f"height={original_height}",      # Exact match
                            f"height<={original_height}",     # Equal or lower
                        ]

                        # Add specific lower quality options based on common heights
                        if original_height >= 720:
                            fallback_selectors.extend(["height=720", "height=480", "height=360"])
                        elif original_height >= 480:
                            fallback_selectors.extend(["height=480", "height=360"])
                        elif original_height >= 360:
                            fallback_selectors.append("height=360")
                    else:
                        # Default fallback if we can't parse the quality
                        fallback_selectors = ["height=720", "height=480", "height=360"]

                    print(f"🎯 Original quality: {quality}, using fallback selectors: {fallback_selectors}")

                    for i, selector in enumerate(fallback_selectors, 1):
                        print(f"🔄 Retry {i}: Using video selector '{selector}' (respecting quality choice)")

                        # Update command with new selector
                        retry_command = download_command.copy()
                        # Find and update --select-video parameter (position may vary)
                        for idx, arg in enumerate(retry_command):
                            if arg == "--select-video" and idx + 1 < len(retry_command):
                                retry_command[idx + 1] = selector
                                break

                        print(f"🚀 Retry command:")
                        for j, arg in enumerate(retry_command):
                            print(f"   [{j}] {arg}")

                        # Execute retry
                        retry_process = subprocess.Popen(
                            retry_command,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,  # Separate stderr for consistency
                            text=True,
                            universal_newlines=True,
                            bufsize=1,
                            cwd=str(cache_dir)
                        )

                        # Monitor retry progress (read both stdout and stderr)
                        while True:
                            output = retry_process.stdout.readline()
                            error = retry_process.stderr.readline()

                            if output == '' and error == '' and retry_process.poll() is not None:
                                break

                            if output:
                                print(f"   📤 {output.strip()}")
                            if error:
                                print(f"   ⚠️ {error.strip()}")

                        retry_return_code = retry_process.returncode
                        print(f"   🏁 Retry finished with return code: {retry_return_code}")

                        if retry_return_code == 0:
                            # Check again for video files
                            video_files = list(cache_dir.glob(f"{filename}*.mp4"))
                            if video_files:
                                print(f"✅ Video download successful with selector '{selector}'")
                                return True
                        else:
                            print(f"❌ Retry {i} failed")

                    # If all retries failed
                    print("❌ All video download attempts failed")
                    return False
                else:
                    print(f"✅ Video files found: {len(video_files)} files")
                    return True
            else:
                print("❌ Download failed")
                print("   📋 Last output lines:")
                for line in output_lines[-10:]:
                    print(f"      {line}")

                # Emit error signal with details
                self.download_error.emit(f"Download failed with return code: {return_code}")
                return False

        except Exception as e:
            print(f"❌ N_m3u8DL-RE error: {str(e)}")
            import traceback
            traceback.print_exc()
            return False



    def merge_with_mkvmerge(self, video_file, audio_files, subtitle_files, output_file):
        """Merge video, audio and subtitles using mkvmerge"""
        try:
            merge_command = [
                str(self.mkvmerge_path), "-o", str(output_file),
                str(video_file)
            ]

            # Add audio files with proper track names
            for audio_file in audio_files:
                if audio_file.exists():
                    # Detect language and track name from filename
                    if "ara" in str(audio_file).lower():
                        language = "ara"
                        track_name = "Arabic"
                    elif "eng" in str(audio_file).lower():
                        language = "eng"
                        track_name = "English"
                    elif "fra" in str(audio_file).lower():
                        language = "fra"
                        track_name = "French"
                    elif "tur" in str(audio_file).lower():
                        language = "tur"
                        track_name = "Turkish"
                    else:
                        language = "und"  # undefined
                        track_name = "Unknown"

                    merge_command.extend([
                        "--language", f"0:{language}",
                        "--track-name", f"0:{track_name}",
                        str(audio_file)
                    ])
                    print(f"🔊 Adding audio: {audio_file.name} (language: {language}, name: {track_name})")

            # Add subtitle files
            for subtitle_file in subtitle_files:
                if subtitle_file.exists():
                    # Detect language from filename
                    if "ara" in str(subtitle_file).lower() or "arabic" in str(subtitle_file).lower():
                        language = "ara"
                    elif "eng" in str(subtitle_file).lower() or "english" in str(subtitle_file).lower():
                        language = "eng"
                    elif "fra" in str(subtitle_file).lower() or "french" in str(subtitle_file).lower():
                        language = "fra"
                    elif "tur" in str(subtitle_file).lower() or "turkish" in str(subtitle_file).lower():
                        language = "tur"
                    else:
                        language = "und"  # undefined

                    merge_command.extend([
                        "--language", f"0:{language}",
                        "--track-name", f"0:{language.upper()} Subtitles",
                        str(subtitle_file)
                    ])
                    print(f"📝 Adding subtitle: {subtitle_file.name} (language: {language})")

            print(f"🔧 Merge command: {' '.join(merge_command)}")

            # Execute merge
            result = subprocess.run(merge_command, capture_output=True, text=True)

            if result.returncode == 0:
                print(f"✅ Merge completed: {output_file}")
                return True
            else:
                print(f"❌ Merge failed: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ Merge error: {str(e)}")
            return False

    def format_quality_for_filename(self, quality):
        """Format quality properly for filename with 4K/HD separation"""
        try:
            # Handle different quality formats
            if 'x' in quality:
                # Format like "640x268" or "3840x1608" - convert to standard format
                width, height = quality.split('x')
                width = int(width)
                height = int(height)

                # Use the same classification logic as the UI
                # Special YANGO resolution mappings (exact matches first)
                if height == 960:  # 1920x960
                    return '1080p'
                elif height == 812:  # 1920x812 (new format)
                    return '1080p'
                elif height == 640:  # 1280x640
                    return '720p'
                elif height == 542:  # 1280x542 (new format)
                    return '720p'
                elif height == 512:  # 1024x512
                    return '520p'
                elif height == 434:  # 1024x434 (new format)
                    return '430p'
                elif height == 428:  # 854x428
                    return '430p'
                elif height == 362:  # 854x362 (new format)
                    return '360p'
                elif height == 320:  # 640x320
                    return '320p'
                elif height == 270:  # 640x270 (new format)
                    return '260p'

                # 4K Quality Classification (separate from HD) - specific resolutions first
                elif width == 3840 and height == 1608:  # Ultra-Wide 4K
                    return "2160p"
                elif width == 2560 and height == 1072:  # Ultra-Wide 1440p (2K)
                    return "1440p"
                elif height >= 2160:  # Standard 4K
                    return "2160p"
                elif height >= 1608:  # Ultra-Wide 4K range
                    return "2160p"
                elif height >= 1440:  # 2K/QHD
                    return "1440p"

                # HD Quality Classification (traditional ranges)
                elif height >= 1080:  # Full HD
                    return "1080p"
                elif height >= 720:  # HD
                    return "720p"
                elif height >= 576:  # PAL
                    return "576p"
                elif height >= 480:  # NTSC
                    return "480p"
                elif height >= 430:  # Enhanced SD
                    return "430p"
                elif height >= 360:  # Low quality
                    return "360p"
                elif height >= 268:  # Very low quality
                    return "260p"
                else:
                    return f"{height}p"

            elif quality.endswith('p'):
                # Already in correct format like "720p"
                return quality
            else:
                # Add 'p' if missing
                return f"{quality}p"

        except Exception as e:
            print(f"⚠️ Error formatting quality '{quality}': {e}")
            # Fallback to original quality with 'p' added if needed
            return quality if quality.endswith('p') else f"{quality}p"

    def sanitize_filename(self, filename):
        """Make filename safe for filesystem"""
        import re
        # Remove invalid characters
        filename = re.sub(r'[<>:"/\\|?*]', '', filename)
        # Replace spaces with dots
        filename = filename.replace(' ', '.')
        # Remove multiple dots
        filename = re.sub(r'\.+', '.', filename)
        return filename.strip('.')

    def load_settings(self):
        """Load settings manager"""
        try:
            from .yango_settings import YangoSettings
            self.settings_manager = YangoSettings()
            print("✅ Settings loaded in downloader")
        except Exception as e:
            print(f"❌ Error loading settings in downloader: {e}")
            self.settings_manager = None

    def get_filename_format(self, content_type="movie"):
        """Get filename format from settings"""
        try:
            if self.settings_manager:
                return self.settings_manager.get_filename_format(content_type)
            else:
                # Default formats if settings not available
                if content_type == "movie":
                    return "{title}.{quality}.YANGO.WEB-DL.H264.AAC"
                else:
                    return "{title}.{season}.{episode}.{quality}.YANGO.WEB-DL.H264.AAC"
        except Exception as e:
            print(f"❌ Error getting filename format: {e}")
            # Fallback to default
            if content_type == "movie":
                return "{title}.{quality}.YANGO.WEB-DL.H264.AAC"
            else:
                return "{title}.{season}.{episode}.{quality}.YANGO.WEB-DL.H264.AAC"

    def get_proxy_config(self):
        """Get proxy configuration from settings"""
        try:
            if self.settings_manager:
                return self.settings_manager.get_proxy_config()
            return None
        except Exception as e:
            print(f"❌ Error getting proxy config: {e}")
            return None

    def get_device_path(self):
        """Get device.wvd path from settings"""
        try:
            if self.settings_manager:
                return self.settings_manager.get_device_path()
            return "device.wvd"
        except Exception as e:
            print(f"❌ Error getting device path: {e}")
            return "device.wvd"

    def clear_completed_downloads(self):
        """Clear completed downloads from queue"""
        # This will be implemented in the UI
        pass

    def clear_all_downloads(self):
        """Clear all downloads from queue"""
        self.download_queue.clear()
        print("🧹 Cleared all downloads from queue")

    def force_stop_downloads(self):
        """Force stop all downloads and prevent restart"""
        try:
            print("🛑 Force stopping all downloads...")

            # Set stopping flag to prevent new downloads
            self.is_stopping = True

            # Clear download queue
            self.download_queue.clear()
            self.current_download = None

            # Terminate any running processes with better cleanup
            if hasattr(self, 'current_process') and self.current_process:
                try:
                    print("🛑 Terminating current download process...")

                    # First try graceful termination
                    self.current_process.terminate()
                    try:
                        self.current_process.wait(timeout=5)
                        print("✅ Process terminated gracefully")
                    except subprocess.TimeoutExpired:
                        print("⚠️ Process didn't terminate gracefully, forcing kill...")
                        self.current_process.kill()
                        try:
                            self.current_process.wait(timeout=2)
                            print("✅ Process killed successfully")
                        except subprocess.TimeoutExpired:
                            print("❌ Process couldn't be killed")

                except Exception as e:
                    print(f"⚠️ Error terminating process: {e}")
                finally:
                    self.current_process = None

            # Kill any remaining N_m3u8DL-RE processes
            self.kill_remaining_processes()

            # Stop the thread
            if self.isRunning():
                print("🛑 Stopping download thread...")
                self.terminate()
                if not self.wait(5000):  # Wait up to 5 seconds
                    print("⚠️ Thread didn't stop gracefully, forcing...")
                    self.kill()

            print("🛑 All downloads force stopped")

        except Exception as e:
            print(f"❌ Error force stopping downloads: {str(e)}")

    def kill_remaining_processes(self):
        """Kill any remaining N_m3u8DL-RE processes"""
        try:
            import psutil

            # Find and kill N_m3u8DL-RE processes
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if 'N_m3u8DL-RE' in proc.info['name']:
                        print(f"🛑 Killing remaining N_m3u8DL-RE process: {proc.info['pid']}")
                        proc.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

        except ImportError:
            # psutil not available, use system command
            try:
                import os
                if os.name == 'nt':  # Windows
                    os.system('taskkill /f /im N_m3u8DL-RE.exe >nul 2>&1')
                else:  # Unix-like
                    os.system('pkill -f N_m3u8DL-RE')
                print("🛑 Killed remaining processes using system command")
            except Exception as e:
                print(f"⚠️ Could not kill remaining processes: {e}")

    def reset_stopping_flag(self):
        """Reset stopping flag to allow new downloads"""
        self.is_stopping = False
        print("🔄 Download stopping flag reset - ready for new downloads")
