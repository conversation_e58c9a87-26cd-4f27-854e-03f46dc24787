# 🎬 Unified Streaming Downloader Hub

## 🌟 Overview

This is the **Unified Streaming Downloader Hub** - a single application that provides access to multiple streaming platform downloaders through an integrated sidebar menu.

## 🎯 Features

### 🎬 OSN+ Downloader
- **Launch:** Click "🎬 OSN+" in the sidebar
- **Features:** Download movies and series from OSN+ platform
- **Quality:** Support for 4K, 1080p, 720p, and more
- **DRM:** Advanced DRM content support

### 🎵 YANGO Downloader  
- **Launch:** Click "🎵 YANGO" in the sidebar (Current Application)
- **Features:** Download music and videos from YANGO platform
- **Quality:** High-quality downloads with metadata
- **Formats:** Multiple audio and video formats

### 📺 Netflix Downloader
- **Status:** Coming Soon
- **Launch:** Click "📺 Netflix" in the sidebar
- **Features:** Will support Netflix content downloading

### ⚙️ Settings
- **Access:** Click "⚙️ Settings" in the sidebar
- **Features:** Configure application preferences

## 🚀 How to Use

### 1. Launch the Hub
```bash
cd YAN<PERSON><PERSON>
python main.py
```

### 2. Select Platform
- Use the **sidebar menu** on the left
- Click on any platform icon to access that downloader
- Each platform opens in its own dedicated interface

### 3. Platform-Specific Usage

#### OSN+ Platform:
1. Click "🎬 OSN+" in sidebar
2. OSN+ application will launch automatically
3. Use the OSN+ interface for downloading

#### YANGO Platform:
1. Click "🎵 YANGO" in sidebar (default view)
2. Enter streaming URL or content ID
3. Select quality and download

## 🎨 Interface Design

### Sidebar Menu
- **🎬 OSN+** - Launch OSN+ downloader
- **🎵 YANGO** - Access YANGO downloader (current)
- **📺 Netflix** - Future Netflix support
- **⚙️ Settings** - Application settings
- **❌ Exit** - Close application

### Main Content Area
- **Search Bar** - Universal search for streaming content
- **Quality Selection** - Choose download quality
- **Content Display** - Show found movies/series
- **Download Management** - Track download progress

## 🔧 Technical Details

### Architecture
- **Base Application:** YANGO framework
- **Platform Integration:** Subprocess launching for external apps
- **UI Framework:** PySide6 (Qt for Python)
- **Theme:** Dark theme with modern design

### File Structure
```
YANGO/
├── main.py                 # Main unified application
├── modules/
│   ├── ui_main.py         # UI definitions
│   ├── yango_api.py       # YANGO API handler
│   ├── yango_ui.py        # YANGO UI logic
│   └── yango_downloader.py # Download manager
├── config/                # Configuration files
├── downloads/             # Downloaded content
├── cache/                 # Temporary files
└── binaries/              # Required tools
```

## 🎯 Platform Status

| Platform | Status | Features |
|----------|--------|----------|
| 🎵 YANGO | ✅ Active | Full download support |
| 🎬 OSN+ | ✅ Integrated | External app launch |
| 📺 Netflix | 🔄 Coming Soon | Future development |

## 🔄 Updates

### Current Version Features:
- ✅ Unified sidebar navigation
- ✅ OSN+ application integration
- ✅ YANGO downloader (native)
- ✅ Modern dark theme UI
- ✅ Quality selection dropdown
- ✅ Universal search functionality

### Planned Features:
- 🔄 Netflix downloader integration
- 🔄 Additional streaming platforms
- 🔄 Enhanced download management
- 🔄 Cross-platform content search

## 🛠️ Requirements

- **Python 3.8+**
- **PySide6** (Qt for Python)
- **Required tools** in binaries folder
- **Platform-specific dependencies**

## 🚀 Quick Start

1. **Install Dependencies:**
   ```bash
   pip install PySide6 requests
   ```

2. **Launch Application:**
   ```bash
   python main.py
   ```

3. **Select Platform:**
   - Click sidebar menu items
   - Each platform opens automatically

## 📝 Notes

- **OSN+ Integration:** Launches external OSN+ application
- **YANGO Native:** Built-in YANGO downloader
- **Settings Sync:** Shared configuration across platforms
- **Theme Consistency:** Unified dark theme design

---

**Unified Streaming Downloader Hub** - One interface, multiple platforms! 🎬🎵📺
