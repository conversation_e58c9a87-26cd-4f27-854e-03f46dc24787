﻿LOG 2025/06/08
Save Path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\Logs
Task Start: 2025/06/08 22:58:16
Task CommandLine: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\N_m3u8DL-RE.exe https://osn-video.anghcdn.co/public/154157-57496-PR681508-BX-AS045366-283428-ef479147eab0bffe7b96aef7d660a211-1749041409/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD01NzQ5NiZleHBpcnk9MTc0OTQ1NTY2NiZzaWduYXR1cmU9MGIzZDE2NmUzYzgyYTFmYWRmZjlkOGU4ZjEzMmNjNmRlN2ExZjQ5ZiZzdHJlYW0taWQ9MTI4MDAxJnVzZXItaWQ9MTAzMDI2MDY5 -mt --select-video id=7b5fa728-c50f-4eb7-b887-5466e16bff28 --select-audio lang=ar --drop-subtitle .* --tmp-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-name "Al Mushardoon S01E27.720p.OSN+.WEB-DL.H264.AAC" --decryption-binary-path=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\mp4decrypt.exe --key-text-file=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\KEYS\OSNPLUS_KEYS.txt --log-level OFF

22:58:16.848 EXTRA: ffmpeg => C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\ffmpeg.exe
22:58:17.189 EXTRA: DropSubtitleFilter => For: best
22:58:17.189 EXTRA: VideoFilter => GroupIdReg: 7b5fa728-c50f-4eb7-b887-5466e16bff28 For: best
22:58:17.190 EXTRA: AudioFilter => LanguageReg: ar For: best
