/* /////////////////////////////////////////////////////////////////////////////////////////////////

SET APP STYLESHEET - FULL STYLES HERE
DARK THEME - DRACULA COLOR BASED

# BY: WANDERSON M.PIMENTA
# PROJECT MADE WITH: Qt Designer and PySide6
# V: 1.0.0
#
# This project can be used freely for all uses, as long as they maintain the
# respective credits only in the Python scripts, any information in the visual
# interface (GUI) can be modified without any implication.
#
# There are limitations on Qt licenses if you want to use your products
# commercially, I recommend reading them on the official website:
# https://doc.qt.io/qtforpython/licenses.html

///////////////////////////////////////////////////////////////////////////////////////////////// */

QWidget{
	color: rgb(221, 221, 221);
	font: 10pt "Segoe UI";
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Tooltip */
QToolTip {
	color: #ffffff;
	background-color: rgba(33, 37, 43, 180);
	border: 1px solid rgb(44, 49, 58);
	background-image: none;
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 2px solid rgb(255, 121, 198);
	text-align: left;
	padding-left: 8px;
	margin: 0px;
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Bg App */
#bgApp {
	background-color: rgb(40, 44, 52);
	border: 1px solid rgb(44, 49, 58);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Left Menu */
#leftMenuBg {
	background-color: rgb(33, 37, 43);
}
#topLogo {
	background-color: rgb(33, 37, 43);
	background-position: centered;
	background-repeat: no-repeat;
}
#titleLeftApp { font: 63 12pt "Segoe UI Semibold"; }
#titleLeftDescription { font: 8pt "Segoe UI"; color: rgb(189, 147, 249); }

/* MENUS */
#topMenu .QPushButton {
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 22px solid transparent;
	background-color: transparent;
	text-align: left;
	padding-left: 44px;
}
#topMenu .QPushButton:hover {
	background-color: rgb(40, 44, 52);
}
#topMenu .QPushButton:pressed {
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}
#bottomMenu .QPushButton {
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 20px solid transparent;
	background-color:transparent;
	text-align: left;
	padding-left: 44px;
}
#bottomMenu .QPushButton:hover {
	background-color: rgb(40, 44, 52);
}
#bottomMenu .QPushButton:pressed {
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}
#leftMenuFrame{
	border-top: 3px solid rgb(44, 49, 58);
}

/* Toggle Button */
#toggleButton {
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 20px solid transparent;
	background-color: rgb(37, 41, 48);
	text-align: left;
	padding-left: 44px;
	color: rgb(113, 126, 149);
}
#toggleButton:hover {
	background-color: rgb(40, 44, 52);
}
#toggleButton:pressed {
	background-color: rgb(189, 147, 249);
}

/* Title Menu */
#titleRightInfo { padding-left: 10px; }


/* /////////////////////////////////////////////////////////////////////////////////////////////////
Extra Tab */
#extraLeftBox {
	background-color: rgb(44, 49, 58);
}
#extraTopBg{
	background-color: rgb(189, 147, 249)
}

/* Icon */
#extraIcon {
	background-position: center;
	background-repeat: no-repeat;
	background-image: url(:/icons/images/icons/icon_settings.png);
}

/* Label */
#extraLabel { color: rgb(255, 255, 255); }

/* Btn Close */
#extraCloseColumnBtn { background-color: rgba(255, 255, 255, 0); border: none;  border-radius: 5px; }
#extraCloseColumnBtn:hover { background-color: rgb(196, 161, 249); border-style: solid; border-radius: 4px; }
#extraCloseColumnBtn:pressed { background-color: rgb(180, 141, 238); border-style: solid; border-radius: 4px; }

/* Extra Content */
#extraContent{
	border-top: 3px solid rgb(40, 44, 52);
}

/* Extra Top Menus */
#extraTopMenu .QPushButton {
background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 22px solid transparent;
	background-color:transparent;
	text-align: left;
	padding-left: 44px;
}
#extraTopMenu .QPushButton:hover {
	background-color: rgb(40, 44, 52);
}
#extraTopMenu .QPushButton:pressed {
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Content App */
#contentTopBg{
	background-color: rgb(33, 37, 43);
}
#contentBottom{
	border-top: 3px solid rgb(44, 49, 58);
}

/* Top Buttons */
#rightButtons .QPushButton { background-color: rgba(255, 255, 255, 0); border: none;  border-radius: 5px; }
#rightButtons .QPushButton:hover { background-color: rgb(44, 49, 57); border-style: solid; border-radius: 4px; }
#rightButtons .QPushButton:pressed { background-color: rgb(23, 26, 30); border-style: solid; border-radius: 4px; }

/* Theme Settings */
#extraRightBox { background-color: rgb(44, 49, 58); }
#themeSettingsTopDetail { background-color: rgb(189, 147, 249); }

/* Bottom Bar */
#bottomBar { background-color: rgb(44, 49, 58); }
#bottomBar QLabel { font-size: 11px; color: rgb(113, 126, 149); padding-left: 10px; padding-right: 10px; padding-bottom: 2px; }

/* CONTENT SETTINGS */
/* MENUS */
#contentSettings .QPushButton {
	background-position: left center;
    background-repeat: no-repeat;
	border: none;
	border-left: 22px solid transparent;
	background-color:transparent;
	text-align: left;
	padding-left: 44px;
}
#contentSettings .QPushButton:hover {
	background-color: rgb(40, 44, 52);
}
#contentSettings .QPushButton:pressed {
	background-color: rgb(189, 147, 249);
	color: rgb(255, 255, 255);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
QTableWidget */
QTableWidget {
	background-color: transparent;
	padding: 10px;
	border-radius: 5px;
	gridline-color: rgb(44, 49, 58);
	border-bottom: 1px solid rgb(44, 49, 60);
}
QTableWidget::item{
	border-color: rgb(44, 49, 60);
	padding-left: 5px;
	padding-right: 5px;
	gridline-color: rgb(44, 49, 60);
}
QTableWidget::item:selected{
	background-color: rgb(189, 147, 249);
}
QHeaderView::section{
	background-color: rgb(33, 37, 43);
	max-width: 30px;
	border: 1px solid rgb(44, 49, 58);
	border-style: none;
    border-bottom: 1px solid rgb(44, 49, 60);
    border-right: 1px solid rgb(44, 49, 60);
}
QTableWidget::horizontalHeader {
	background-color: rgb(33, 37, 43);
}
QHeaderView::section:horizontal
{
    border: 1px solid rgb(33, 37, 43);
	background-color: rgb(33, 37, 43);
	padding: 3px;
	border-top-left-radius: 7px;
    border-top-right-radius: 7px;
}
QHeaderView::section:vertical
{
    border: 1px solid rgb(44, 49, 60);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
LineEdit */
QLineEdit {
	background-color: rgb(33, 37, 43);
	border-radius: 5px;
	border: 2px solid rgb(33, 37, 43);
	padding-left: 10px;
	selection-color: rgb(255, 255, 255);
	selection-background-color: rgb(255, 121, 198);
}
QLineEdit:hover {
	border: 2px solid rgb(64, 71, 88);
}
QLineEdit:focus {
	border: 2px solid rgb(91, 101, 124);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
PlainTextEdit */
QPlainTextEdit {
	background-color: rgb(27, 29, 35);
	border-radius: 5px;
	padding: 10px;
	selection-color: rgb(255, 255, 255);
	selection-background-color: rgb(255, 121, 198);
}
QPlainTextEdit  QScrollBar:vertical {
    width: 8px;
 }
QPlainTextEdit  QScrollBar:horizontal {
    height: 8px;
 }
QPlainTextEdit:hover {
	border: 2px solid rgb(64, 71, 88);
}
QPlainTextEdit:focus {
	border: 2px solid rgb(91, 101, 124);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
ScrollBars */
QScrollBar:horizontal {
    border: none;
    background: rgb(52, 59, 72);
    height: 8px;
    margin: 0px 21px 0 21px;
	border-radius: 0px;
}
QScrollBar::handle:horizontal {
    background: rgb(189, 147, 249);
    min-width: 25px;
	border-radius: 4px
}
QScrollBar::add-line:horizontal {
    border: none;
    background: rgb(55, 63, 77);
    width: 20px;
	border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    subcontrol-position: right;
    subcontrol-origin: margin;
}
QScrollBar::sub-line:horizontal {
    border: none;
    background: rgb(55, 63, 77);
    width: 20px;
	border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    subcontrol-position: left;
    subcontrol-origin: margin;
}
QScrollBar::up-arrow:horizontal, QScrollBar::down-arrow:horizontal
{
     background: none;
}
QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal
{
     background: none;
}
 QScrollBar:vertical {
	border: none;
    background: rgb(52, 59, 72);
    width: 8px;
    margin: 21px 0 21px 0;
	border-radius: 0px;
 }
 QScrollBar::handle:vertical {
	background: rgb(189, 147, 249);
    min-height: 25px;
	border-radius: 4px
 }
 QScrollBar::add-line:vertical {
     border: none;
    background: rgb(55, 63, 77);
     height: 20px;
	border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
     subcontrol-position: bottom;
     subcontrol-origin: margin;
 }
 QScrollBar::sub-line:vertical {
	border: none;
    background: rgb(55, 63, 77);
     height: 20px;
	border-top-left-radius: 4px;
    border-top-right-radius: 4px;
     subcontrol-position: top;
     subcontrol-origin: margin;
 }
 QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
     background: none;
 }

 QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
     background: none;
 }

/* /////////////////////////////////////////////////////////////////////////////////////////////////
CheckBox */
QCheckBox::indicator {
    border: 3px solid rgb(52, 59, 72);
	width: 15px;
	height: 15px;
	border-radius: 10px;
    background: rgb(44, 49, 60);
}
QCheckBox::indicator:hover {
    border: 3px solid rgb(58, 66, 81);
}
QCheckBox::indicator:checked {
    background: 3px solid rgb(52, 59, 72);
	border: 3px solid rgb(52, 59, 72);
	background-image: url(:/icons/images/icons/cil-check-alt.png);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
RadioButton */
QRadioButton::indicator {
    border: 3px solid rgb(52, 59, 72);
	width: 15px;
	height: 15px;
	border-radius: 10px;
    background: rgb(44, 49, 60);
}
QRadioButton::indicator:hover {
    border: 3px solid rgb(58, 66, 81);
}
QRadioButton::indicator:checked {
    background: 3px solid rgb(94, 106, 130);
	border: 3px solid rgb(52, 59, 72);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
ComboBox */
QComboBox{
	background-color: rgb(27, 29, 35);
	border-radius: 5px;
	border: 2px solid rgb(33, 37, 43);
	padding: 5px;
	padding-left: 10px;
}
QComboBox:hover{
	border: 2px solid rgb(64, 71, 88);
}
QComboBox::drop-down {
	subcontrol-origin: padding;
	subcontrol-position: top right;
	width: 25px;
	border-left-width: 3px;
	border-left-color: rgba(39, 44, 54, 150);
	border-left-style: solid;
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;
	background-image: url(:/icons/images/icons/cil-arrow-bottom.png);
	background-position: center;
	background-repeat: no-reperat;
 }
QComboBox QAbstractItemView {
	color: rgb(255, 121, 198);
	background-color: rgb(33, 37, 43);
	padding: 10px;
	selection-background-color: rgb(39, 44, 54);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Sliders */
QSlider::groove:horizontal {
    border-radius: 5px;
    height: 10px;
	margin: 0px;
	background-color: rgb(52, 59, 72);
}
QSlider::groove:horizontal:hover {
	background-color: rgb(55, 62, 76);
}
QSlider::handle:horizontal {
    background-color: rgb(189, 147, 249);
    border: none;
    height: 10px;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}
QSlider::handle:horizontal:hover {
    background-color: rgb(195, 155, 255);
}
QSlider::handle:horizontal:pressed {
    background-color: rgb(255, 121, 198);
}

QSlider::groove:vertical {
    border-radius: 5px;
    width: 10px;
    margin: 0px;
	background-color: rgb(52, 59, 72);
}
QSlider::groove:vertical:hover {
	background-color: rgb(55, 62, 76);
}
QSlider::handle:vertical {
    background-color: rgb(189, 147, 249);
	border: none;
    height: 10px;
    width: 10px;
    margin: 0px;
	border-radius: 5px;
}
QSlider::handle:vertical:hover {
    background-color: rgb(195, 155, 255);
}
QSlider::handle:vertical:pressed {
    background-color: rgb(255, 121, 198);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
CommandLinkButton */
QCommandLinkButton {
	color: rgb(255, 121, 198);
	border-radius: 5px;
	padding: 5px;
}
QCommandLinkButton:hover {
	color: rgb(255, 170, 255);
	background-color: rgb(44, 49, 60);
}
QCommandLinkButton:pressed {
	color: rgb(189, 147, 249);
	background-color: rgb(52, 58, 71);
}

/* /////////////////////////////////////////////////////////////////////////////////////////////////
Button */
#pagesContainer QPushButton {
	border: 2px solid rgb(52, 59, 72);
	border-radius: 5px;
	background-color: rgb(52, 59, 72);
}
#pagesContainer QPushButton:hover {
	background-color: rgb(57, 65, 80);
	border: 2px solid rgb(61, 70, 86);
}
#pagesContainer QPushButton:pressed {
	background-color: rgb(35, 40, 49);
	border: 2px solid rgb(43, 50, 61);
}



