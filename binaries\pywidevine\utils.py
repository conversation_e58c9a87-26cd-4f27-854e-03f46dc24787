import glob, shutil, os, re

def empty_folder(folder):
    files = glob.glob('%s/*'%folder)
    for f in files:
        os.remove(f)

def divider():
    count = int(shutil.get_terminal_size().columns)
    count = count - 1
    print ('=' * count)

def filename(X):
    X = X.replace(',','').replace('/','.').replace(':','').replace(';','').replace(' -','').replace('?','').replace(' ','.').replace('..','.').replace('ii','II').replace('iii','III')
    return X

def vtt_to_srt(vtt):
    # Remove any WEBVTT header if present
    vtt = vtt.replace("WEBVTT\n", "")

    # Replace VTT-specific timestamps with SRT-style timestamps
    srt = vtt.replace(".", ",")

    return srt

def remove_style(vtt_content):
    # return re.sub(r'^\s*STYLE\s*\n[\s\S]*?(?=\n\n|\Z)', '', vtt_content, flags=re.MULTILINE)
    return re.sub(r'^\s*STYLE\s*\n[\s\S]*?(?=\n\n|\Z)|^\s*NOTE video_app_international_ai.*$', '', vtt_content, flags=re.MULTILINE)

def convert_vtt_to_srt(input_directory, output_directory):
    if not os.path.exists(output_directory):
        os.makedirs(output_directory)

    for filename in os.listdir(input_directory):
        if filename.endswith(".vtt"):
            input_file_path = os.path.join(input_directory, filename)
            output_file_path = os.path.join(output_directory, filename[:-4] + ".srt")

            with open(input_file_path, 'r', encoding='utf-8') as vtt_file:
                vtt_content = vtt_file.read()

            # Remove STYLE section
            vtt_content = remove_style(vtt_content)

            srt = vtt_to_srt(vtt_content)

            with open(output_file_path, 'w', encoding='utf-8') as srt_file:
                srt_file.write(srt)

def format_boxed_title(tools_name, padding=6):
    box_width = len(tools_name) + 2 * padding

    decorative_line = "═" * box_width

    formatted_output = f"""
   ╔{decorative_line}╗
   ║{' ' * padding}{tools_name}{' ' * (box_width - len(tools_name) - padding)}║
   ╚{decorative_line}╝
   """
    return formatted_output

def force_instance(x):
    if isinstance(x['Representation'], list):
        X = x['Representation']
    else:
        X = [x['Representation']]
    return X

def alphanumericSort(l):
    def convert(text):
        if text.isdigit():
            return int(text)
        else:
            return text

    def alphanum_key(key):
        return [convert(c) for c in re.split('([0-9]+)', key)]

    return sorted(l, key=alphanum_key)

def strip_jsonp(code):
    return re.sub(
        r'''(?sx)^
            (?:window\.)?(?P<func_name>[a-zA-Z0-9_.$]*)
            (?:\s*&&\s*(?P=func_name))?
            \s*\(\s*(?P<callback_data>.*)\);?
            \s*?(?://[^\n]*)*$''',
        r'\g<callback_data>', code)

def determine_ext(url, default_ext='unknown_video'):
    if url is None or '.' not in url:
        return default_ext
    guess = url.partition('?')[0].rpartition('.')[2]
    if re.match(r'^[A-Za-z0-9]+$', guess):
        return guess
    # Try extract ext from URLs like http://example.com/foo/bar.mp4/?download
    elif guess.rstrip('/') in KNOWN_EXTENSIONS:
        return guess.rstrip('/')
    else:
        return default_ext

def int_or_none(v, scale=1, default=None, get_attr=None, invscale=1):
    if get_attr:
        if v is not None:
            v = getattr(v, get_attr, None)
    if v == '':
        v = None
    if v is None:
        return default
    try:
        return int(v) * invscale // scale
    except (ValueError, TypeError, OverflowError):
        return default

def float_or_none(v, scale=1, invscale=1, default=None):
    if v is None:
        return default
    try:
        return float(v) * invscale / scale
    except (ValueError, TypeError):
        return default

def convert_size(size_bytes):
    if size_bytes == 0:
        return '0bps'
    else:
        s = round(size_bytes / 1000, 0)
        return '%ikb/s' % s

def read_keys_file(file_path):
    try:
        with open(file_path, 'r') as file:
            return set(line.strip() for line in file)
    except FileNotFoundError:
        return set()

def get_binary_path(binary_name):
    """Get path to binary executable"""
    import shutil
    return shutil.which(binary_name)