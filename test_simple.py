#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

print("🔍 Starting YANGO Test...")
print(f"Python version: {sys.version}")
print(f"Current directory: {os.getcwd()}")

try:
    print("📦 Testing PyQt5 import...")
    from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel
    from PyQt5.QtCore import Qt
    print("✅ PyQt5 imported successfully!")
    
    print("🎮 Creating QApplication...")
    app = QApplication(sys.argv)
    print("✅ QApplication created!")
    
    print("🪟 Creating main window...")
    window = QMainWindow()
    window.setWindowTitle("YANGO Test Window")
    window.setGeometry(100, 100, 400, 300)
    
    label = QLabel("YANGO Test - If you see this, PyQt5 works!", window)
    label.setAlignment(Qt.AlignCenter)
    window.setCentralWidget(label)
    
    print("✅ Window created!")
    print("📺 Showing window...")
    window.show()
    
    print("🚀 Starting event loop...")
    sys.exit(app.exec_())
    
except ImportError as e:
    print(f"❌ Import Error: {e}")
    input("Press Enter to exit...")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
