#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Shahid Window Player - Opens in separate window with JWPlayer fixes
"""

import os
import threading
import urllib.parse
import http.server
import socketserver
import time
from PySide6.QtCore import QO<PERSON>, Signal, QUrl, Qt, QTimer
from PySide6.QtWidgets import QDialog, QVBoxLayout, QWidget, QHBoxLayout, QPushButton, QLabel
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtGui import QIcon

class PlayerSignals(QObject):
    """Signals for player events."""
    player_started = Signal(bool)
    player_stopped = Signal()
    player_error = Signal(str)

class SimpleHTTPHandler(http.server.SimpleHTTPRequestHandler):
    """Simple HTTP request handler with custom directory."""
    def __init__(self, *args, directory=None, **kwargs):
        self.directory = directory
        super().__init__(*args, directory=directory, **kwargs)

    def log_message(self, format, *args):
        """Override to customize logging."""
        print(f"[HTTP] {format % args}")

    def end_headers(self):
        """Add CORS headers."""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        # Add cache control headers
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        super().end_headers()

class ShahidPlayerWindow(QDialog):
    """Separate window for Shahid player with JWPlayer fixes."""
    
    def __init__(self, url, parent=None, on_close_callback=None):
        super().__init__(parent)
        self.setWindowTitle("Shahid Player - شاهد")
        self.resize(1280, 720)
        self.player_url = url
        self.on_close_callback = on_close_callback

        # Set window flags for separate window
        self.setWindowFlags(Qt.Window | Qt.WindowMaximizeButtonHint | Qt.WindowCloseButtonHint)

        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create web view
        self.web_view = QWebEngineView()
        
        # Configure web view settings
        self.setup_webview()
        
        # Load URL
        print(f"Loading URL in separate window: {url}")
        self.web_view.load(QUrl(url))
        layout.addWidget(self.web_view)

        # Set dialog to delete on close
        self.setAttribute(Qt.WA_DeleteOnClose)

    def setup_webview(self):
        """Setup web view with JWPlayer compatibility fixes."""
        try:
            from PySide6.QtWebEngineCore import QWebEngineSettings, QWebEngineScript
            
            settings = self.web_view.page().settings()
            
            # Enable all necessary features
            settings.setAttribute(QWebEngineSettings.WebAttribute.PluginsEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.JavascriptEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessRemoteUrls, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessFileUrls, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.AllowRunningInsecureContent, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.LocalStorageEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.WebGLEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.Accelerated2dCanvasEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.AutoLoadImages, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.ErrorPageEnabled, True)
            settings.setAttribute(QWebEngineSettings.WebAttribute.FullScreenSupportEnabled, True)

            # Set user agent
            profile = self.web_view.page().profile()
            profile.setHttpUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

            # Inject JWPlayer fix script
            js_fix = """
                // JWPlayer License Fix
                console.log('Applying JWPlayer compatibility fixes...');
                
                // Override navigator properties
                Object.defineProperty(navigator, 'userAgent', {
                    get: function() {
                        return 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36';
                    },
                    configurable: true
                });
                
                Object.defineProperty(navigator, 'vendor', {
                    get: function() { return 'Google Inc.'; },
                    configurable: true
                });
                
                // Remove Qt properties
                if (window.qt) delete window.qt;
                if (window.qmlEngine) delete window.qmlEngine;
                
                // JWPlayer license override
                window.addEventListener('DOMContentLoaded', function() {
                    if (window.jwplayer) {
                        const originalJwplayer = window.jwplayer;
                        window.jwplayer = function(id) {
                            const instance = originalJwplayer(id);
                            if (instance && instance.setup) {
                                const originalSetup = instance.setup;
                                instance.setup = function(config) {
                                    config.key = 'FREE';
                                    config.analytics = false;
                                    config.advertising = false;
                                    return originalSetup.call(this, config);
                                };
                            }
                            return instance;
                        };
                        Object.keys(originalJwplayer).forEach(key => {
                            if (key !== 'prototype') {
                                window.jwplayer[key] = originalJwplayer[key];
                            }
                        });
                    }
                });
                
                console.log('JWPlayer fixes applied successfully');
            """
            
            script = QWebEngineScript()
            script.setSourceCode(js_fix)
            script.setName("JWPlayerFix")
            script.setWorldId(QWebEngineScript.ScriptWorldId.MainWorld)
            script.setInjectionPoint(QWebEngineScript.InjectionPoint.DocumentCreation)
            script.setRunsOnSubFrames(True)
            
            profile.scripts().insert(script)
            print("✅ JWPlayer fix script injected")
            
        except Exception as e:
            print(f"❌ Error setting up webview: {e}")

    def closeEvent(self, event):
        """Handle window close event."""
        print("Shahid player window closing...")
        
        # Clean up web view
        self.web_view.stop()
        self.web_view.page().deleteLater()
        self.web_view.deleteLater()

        # Call callback if provided
        if self.on_close_callback:
            self.on_close_callback()

        super().closeEvent(event)

class ShahidWindowPlayer:
    """Shahid player that opens in separate window."""
    
    def __init__(self, parent=None):
        self.parent = parent
        self.signals = PlayerSignals()
        self.http_server = None
        self.server_thread = None
        self.player_window = None
        
        # Get player directory
        self.dir_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.player_dir = os.path.join(self.dir_path, 'player-shahid')
        print(f"Player directory: {self.player_dir}")

    def start_http_server(self):
        """Start HTTP server for player files."""
        # Stop any existing server
        self.stop_http_server()
        
        for port in range(8002, 8010):
            try:
                print(f"Starting HTTP server on port {port}...")
                
                handler = lambda *args, **kwargs: SimpleHTTPHandler(
                    *args, directory=self.player_dir, **kwargs
                )
                
                self.http_server = socketserver.TCPServer(("localhost", port), handler)
                self.http_server.allow_reuse_address = True
                
                self.server_thread = threading.Thread(
                    target=self.http_server.serve_forever,
                    daemon=True
                )
                self.server_thread.start()
                
                print(f"✅ HTTP server started on port {port}")
                return port
                
            except OSError as e:
                if "Address already in use" in str(e):
                    continue
                else:
                    print(f"❌ Error starting HTTP server: {e}")
                    break
        
        return None

    def stop_http_server(self):
        """Stop HTTP server."""
        if self.http_server:
            try:
                self.http_server.shutdown()
                self.http_server.server_close()
                print("✅ HTTP server stopped")
            except Exception as e:
                print(f"❌ Error stopping HTTP server: {e}")
            finally:
                self.http_server = None
                self.server_thread = None

    def load_player(self, mpd_url, drm_key=None):
        """Load player in separate window."""
        try:
            print(f"🎬 Loading Shahid player in separate window...")
            print(f"MPD URL: {mpd_url}")
            print(f"DRM Key: {drm_key}")

            # Start HTTP server
            port = self.start_http_server()
            if not port:
                print("❌ Failed to start HTTP server")
                return False

            # Create player URL
            query_params = {'mpd': mpd_url}
            if drm_key and ':' in drm_key:
                key_id, key = drm_key.split(':', 1)
                query_params['keyId'] = key_id
                query_params['key'] = key

            player_url = f"http://localhost:{port}/shahid_player.html?{urllib.parse.urlencode(query_params)}"
            print(f"Player URL: {player_url}")

            # Create and show player window
            self.player_window = ShahidPlayerWindow(
                url=player_url,
                parent=self.parent,
                on_close_callback=self._on_window_closed
            )
            
            self.player_window.showMaximized()
            print("✅ Shahid player window opened")
            
            self.signals.player_started.emit(True)
            return True

        except Exception as e:
            print(f"❌ Error loading player: {e}")
            self.stop_http_server()
            self.signals.player_error.emit(str(e))
            return False

    def _on_window_closed(self):
        """Handle window close callback."""
        print("Player window closed")
        self.player_window = None
        self.stop_http_server()
        self.signals.player_stopped.emit()

    def close(self):
        """Close player."""
        if self.player_window:
            self.player_window.close()
            self.player_window = None
        self.stop_http_server()
