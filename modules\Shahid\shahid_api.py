"""
Shahid API Module
This module handles all API interactions with the Shahid service.
"""

import os
import requests
import re
import hashlib
import hmac
import base64
from datetime import datetime

class ShahidAPI:
    def __init__(self, token=None):
        self.token = token
        # Get the YANGO root directory (3 levels up from modules/Shahid/shahid_api.py)
        self.script_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        # Use paths relative to the YANGO root directory
        proxy_path = os.path.join(self.script_dir, 'proxy.txt')
        self.proxy = self._get_proxy_from_file(proxy_path)
        self.proxies = {'http': self.proxy, 'https': self.proxy} if self.proxy else None

        # Create necessary directories within the YANGO root directory
        os.makedirs(os.path.join(self.script_dir, 'binaries', 'login'), exist_ok=True)
        os.makedirs(os.path.join(self.script_dir, 'KEYS'), exist_ok=True)

    def _get_proxy_from_file(self, file_path):
        """Get proxy from file."""
        try:
            with open(file_path, 'r') as file:
                proxy = file.readline().strip()
                return proxy if proxy else None
        except FileNotFoundError:
            print(f"File {file_path} not found.")
            return None

    def set_token(self, token):
        """Set the API token."""
        self.token = token

    def set_proxies(self, proxies):
        """Set proxies for API requests."""
        self.proxies = proxies

    def _generate_authorization_header(self, data):
        """Generate authorization header for API requests."""
        # Shahid uses a specific algorithm to generate the authorization header
        # This is based on the original Shahid.py implementation
        try:
            # Use the correct secret key
            secret_key = 'z3qQSk17nbajIYUF0dU5f4+O/CxjFizcsEJr9ejOYFw='

            # Create HMAC signature with the correct format
            hmac_result = hmac.new(
                secret_key.encode('utf-8'),
                ";".join(f"{k}={v}" for k, v in sorted(data.items(), key=lambda x: x[0])).replace(' ', '').encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            return hmac_result
        except Exception as e:
            print(f"Error generating authorization header: {e}")
            return ""

    def get_movies_list(self):
        """Get list of movies."""
        # This is a placeholder for now
        return []

    def get_series_list(self):
        """Get list of series."""
        # This is a placeholder for now
        return []

    def extract_content_id_from_url(self, url):
        """Extract content ID from URL or return the ID if it's already an ID."""
        # If the input is already a numeric ID, return it
        if url and url.isdigit():
            print(f"[API] Input is a numeric ID: {url}")
            return url

        # Try to extract ID from URL
        import re

        # Pattern for shahid.mbc.net URLs
        shahid_patterns = [
            # Season URL patterns (extract the first ID from season-ID1-ID2)
            r'season-([0-9]+)-[0-9]+',  # Season URL pattern (season-XXXXXXXXX-YYYYYYYYY)

            # Series URL patterns
            r'shahid\.mbc\.net/ar/series/[^/]+/series-([0-9]+)',  # Arabic series URL
            r'shahid\.mbc\.net/en/series/[^/]+/series-([0-9]+)',  # English series URL
            r'series-([0-9]+)',  # Series ID pattern (series-XXXXXXXXX)
            r'/series/[^/]+/series-([0-9]+)',  # Series URL with any language

            # Show URL patterns
            r'shahid\.mbc\.net/ar/shows/[^/]+/([0-9]+)',  # Arabic show URL
            r'shahid\.mbc\.net/en/shows/[^/]+/([0-9]+)',  # English show URL

            # Movie URL patterns
            r'shahid\.mbc\.net/ar/movies/[^/]+/movie-([0-9]+)',  # Arabic movie URL
            r'shahid\.mbc\.net/en/movies/[^/]+/movie-([0-9]+)',  # English movie URL
            r'movie-([0-9]+)',  # Movie ID pattern (movie-XXXXXXXXX)

            # Generic patterns
            r'shahid\.mbc\.net/[^/]+/[^/]+/[^/]+/([0-9]+)',  # Generic pattern with numeric ID
            r'/([0-9]+)/?$',  # ID at the end of URL
            r'/([0-9]+)/',    # ID in the middle of URL
        ]

        for pattern in shahid_patterns:
            match = re.search(pattern, url)
            if match:
                content_id = match.group(1)
                print(f"[API] Extracted content ID {content_id} from URL using pattern: {pattern}")
                return content_id

        # If no pattern matched, try to find any numeric sequence that looks like an ID
        # Shahid IDs are typically 10-13 digits long
        id_match = re.search(r'[^0-9]([0-9]{10,13})[^0-9]', url + ' ')  # Add space to handle IDs at the end
        if id_match:
            content_id = id_match.group(1)
            print(f"[API] Extracted content ID {content_id} using generic numeric pattern")
            return content_id

        # Try to extract ID from URL like https://shahid.mbc.net/en/series/Harb-El-Gibali/series-4246631591527
        # This pattern looks for a series name followed by a numeric ID at the end
        series_id_pattern = r'/series-([0-9]+)$'
        match = re.search(series_id_pattern, url)
        if match:
            content_id = match.group(1)
            print(f"[API] Extracted content ID {content_id} from URL with series-ID at the end")
            return content_id

        print(f"[API] Could not extract content ID from URL: {url}")
        return None

    def get_content_details(self, content_id_or_url):
        """Get content details from API (works for both movies and series)."""
        print(f"\n[API] Getting content details for: {content_id_or_url}")
        content_id = self.extract_content_id_from_url(content_id_or_url)
        if not content_id:
            print(f"[API ERROR] Invalid URL or ID: {content_id_or_url}")
            return None, "Invalid URL or ID"

        # First, try to get as a series (since most content is series)
        print(f"[API] Trying to get content as SERIES with ID: {content_id}")
        series_details = self.get_series_details(content_id)
        if series_details and 'productModel' in series_details:
            # Verify if it's actually a series by checking for seasons
            product_model = series_details['productModel']

            # IMPORTANT: For Shahid, we'll always treat content as SERIES if it has seasons
            # or if it has specific fields that indicate it's a series
            if 'seasons' in product_model and product_model['seasons']:
                print(f"[API] Content has {len(product_model['seasons'])} seasons, identified as SERIES")
                return series_details, "SERIES"

            # Check for numberOfEpisodes in season
            if 'season' in product_model and 'numberOfEpisodes' in product_model['season']:
                episodes_count = product_model['season'].get('numberOfEpisodes', 0)
                print(f"[API] Content has {episodes_count} episodes in season, identified as SERIES")
                return series_details, "SERIES"

            # Check product type or subtype
            if 'productType' in product_model:
                print(f"[API] Content productType: {product_model['productType']}")
                if product_model['productType'] == "SHOW":
                    return series_details, "SERIES"

            if 'productSubType' in product_model:
                print(f"[API] Content productSubType: {product_model['productSubType']}")
                if product_model['productSubType'] == "SERIES":
                    return series_details, "SERIES"

            if 'showType' in product_model:
                print(f"[API] Content showType: {product_model['showType']}")
                if product_model['showType'] == "SERIES":
                    return series_details, "SERIES"

            # If we still don't know, check for playlists which indicate episodes
            if 'season' in product_model and 'playlists' in product_model['season']:
                playlists = product_model['season']['playlists']
                for playlist in playlists:
                    if playlist.get('type') == 'EPISODE':
                        print(f"[API] Content has episode playlists, identified as SERIES")
                        return series_details, "SERIES"

            # If we still don't know, check title for clues
            if 'title' in product_model:
                title = product_model['title'].lower()
                if 'series' in title or 'season' in title or 'episode' in title:
                    print(f"[API] Content title suggests it's a SERIES: {product_model['title']}")
                    return series_details, "SERIES"

            # For Shahid, if we got this far but the API returned data from the series endpoint,
            # it's most likely a series
            print(f"[API] Content retrieved from series endpoint, defaulting to SERIES")
            return series_details, "SERIES"

        # If not found as series, try as a movie
        print(f"[API] Content not found as SERIES, trying as MOVIE with ID: {content_id}")
        movie_details = self.get_movie_details(content_id)
        if movie_details and 'productModel' in movie_details:
            # Double-check if this "movie" has seasons (sometimes Shahid API returns series data from movie endpoint)
            product_model = movie_details['productModel']
            if 'seasons' in product_model and product_model['seasons']:
                print(f"[API] Content has seasons but was returned from movie endpoint, treating as SERIES")
                return movie_details, "SERIES"

            print(f"[API] Content found as MOVIE")
            return movie_details, "MOVIE"

        print(f"[API ERROR] Content not found with ID: {content_id}")
        return None, "Content not found"

    def find_year_in_object(self, obj, prefix=""):
        """Extract year from various fields in the object."""
        if not isinstance(obj, dict):
            return "Unknown"

        # Direct search for year fields
        year_fields = ["year", "productionYear", "releaseYear"]
        for field in year_fields:
            if field in obj and obj[field]:
                value = obj[field]
                if isinstance(value, (int, str)):
                    return str(value)

        # Search for date fields
        date_fields = ["date", "productionDate", "releaseDate", "airDate"]
        for field in date_fields:
            if field in obj and obj[field]:
                year = self.extract_year_from_date(str(obj[field]))
                if year:
                    return year

        # Search in nested objects
        for key, value in obj.items():
            if isinstance(value, dict):
                year = self.find_year_in_object(value, f"{prefix}.{key}")
                if year != "Unknown":
                    return year
            elif isinstance(value, list):
                for i, item in enumerate(value):
                    if isinstance(item, dict):
                        year = self.find_year_in_object(item, f"{prefix}.{key}[{i}]")
                        if year != "Unknown":
                            return year

        # If created date exists, use it
        if "createdDate" in obj and obj.get("createdDate"):
            created_date = obj.get("createdDate")
            if created_date and isinstance(created_date, str) and len(created_date) >= 4:
                return created_date[:4]

        # If nothing found, return current year
        from datetime import datetime
        return str(datetime.now().year)

    def extract_year_from_date(self, date_str):
        """Extract year from a date string."""
        if not date_str or not isinstance(date_str, str):
            return None

        # Try to extract year from different formats
        # 1. Look for 4 consecutive digits (like 2023)
        import re
        year_match = re.search(r'(19|20)\d{2}', date_str)
        if year_match:
            return year_match.group(0)

        # 2. Try to parse the date using datetime
        try:
            # Try different formats
            from datetime import datetime
            for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%d-%m-%Y', '%d/%m/%Y', '%Y-%m-%dT%H:%M:%S', '%Y-%m-%dT%H:%M:%S.%fZ']:
                try:
                    dt = datetime.strptime(date_str, fmt)
                    return str(dt.year)
                except ValueError:
                    continue
        except Exception:
            pass

        return None

    def get_movie_details(self, movie_id):
        """Get movie details from API."""
        headers = {
            'authority': 'api2.shahid.net',
            'accept': 'application/json',
            'accept-language': 'en',
            'content-type': 'application/json',
            'language': 'en',
            'origin': 'https://shahid.mbc.net',
            'referer': 'https://shahid.mbc.net/',
            'token': self.token,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
            'uuid': 'web',
        }

        params = {
            'request': '{"id":"' + str(movie_id) + '","productType":"MOVIE"}',
            'country': 'SA',
        }

        response = requests.get('https://api3.shahid.net/proxy/v2.1/product/id', params=params, headers=headers, proxies=self.proxies)

        if response.status_code == 200:
            return response.json()
        else:
            return None

    def get_series_details(self, series_id):
        """Get series details from API."""
        headers = {
            'authority': 'api2.shahid.net',
            'accept': 'application/json',
            'accept-language': 'en',
            'content-type': 'application/json',
            'language': 'en',
            'origin': 'https://shahid.mbc.net',
            'referer': 'https://shahid.mbc.net/',
            'token': self.token,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
            'uuid': 'web',
        }

        params = {
            'request': '{"id":"' + str(series_id) + '","productType":"SERIES"}',
            'country': 'SA',
        }

        response = requests.get('https://api3.shahid.net/proxy/v2.1/product/id', params=params, headers=headers, proxies=self.proxies)

        if response.status_code == 200:
            return response.json()
        else:
            return None

    def get_movie_playout_url(self, movie_id):
        """Get movie playout URL."""
        headers = {
            'authority': 'api2.shahid.net',
            'accept': 'application/json',
            'accept-language': 'en',
            'content-type': 'application/json',
            'language': 'en',
            'origin': 'https://shahid.mbc.net',
            'referer': 'https://shahid.mbc.net/',
            'token': self.token,
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
            'uuid': 'web',
        }

        playout_url = f'https://api3.shahid.net/proxy/v2.1/playout/new/url/{movie_id}?outputParameter=vmap&country=EG'

        response = requests.get(playout_url, headers=headers, proxies=self.proxies)

        if response.status_code == 200:
            return response.json()
        else:
            return None

    def get_series_seasons(self, series_id):
        """Get all seasons for a series."""
        # Using the same approach as in the original Shahid.py
        headers = {
            'browser_name': 'CHROME',
            'browser_version': '120.0.0.0',
            'shahid_os': 'WINDOWS',
            'language': 'EN',
            's-session': '',
            'token': self.token
        }

        # First, get the series details to extract seasons
        params = {'request': '{"id":"' + str(series_id) + '"}', 'country': 'EG'}

        print(f"\n[INFO] Fetching series details for series_id: {series_id}")

        try:
            response = requests.get('https://api3.shahid.net/proxy/v2.1/product/id', params=params, headers=headers, proxies=self.proxies)

            if response.status_code == 200:
                response_data = response.json()
                seasons = []

                if 'productModel' in response_data:
                    product = response_data['productModel']

                    # Extract seasons from the product
                    if 'seasons' in product and isinstance(product['seasons'], list):
                        print(f"[INFO] Found {len(product['seasons'])} seasons")

                        for idx, season in enumerate(product['seasons'], start=1):
                            season_id = season.get('id', 'N/A')
                            season_title = season.get('title', f'Season {idx}')
                            season_number = season.get('seasonNumber', idx)

                            # Get episode count from various sources
                            episode_count = 0
                            total_episodes = 0

                            # Try to get from numberOfEpisodes
                            if 'numberOfEpisodes' in season:
                                total_episodes = season.get('numberOfEpisodes', 0)
                                print(f"[INFO] Season {season_number} has numberOfEpisodes: {total_episodes}")

                            # If not found, try numberOfAVODEpisodes
                            if total_episodes == 0 and 'numberOfAVODEpisodes' in season:
                                total_episodes = season.get('numberOfAVODEpisodes', 0)
                                print(f"[INFO] Season {season_number} has numberOfAVODEpisodes: {total_episodes}")

                            # Get detailed season information to get accurate episode count
                            season_params = {'request': '{"id":"' + str(season_id) + '"}', 'country': 'EG'}
                            try:
                                season_response = requests.get('https://api3.shahid.net/proxy/v2.1/product/id', params=season_params, headers=headers, proxies=self.proxies)
                                if season_response.status_code == 200:
                                    season_data = season_response.json()
                                    if 'productModel' in season_data:
                                        season_model = season_data['productModel']

                                        # Look for playlists to get episode count
                                        if 'playlists' in season_model:
                                            season_playlists = season_model['playlists']
                                            for playlist in season_playlists:
                                                playlist_type = playlist.get('type', '')
                                                playlist_title = playlist.get('title', '')
                                                playlist_count = playlist.get('count', 0)
                                                playlist_playlist_type = playlist.get('playlistType', '')

                                                # Look for EPISODE type playlist with title 'Episodes'
                                                if playlist_type == 'EPISODE' and playlist_title == 'Episodes' and playlist_playlist_type != 'FREE_EPISODES':
                                                    episode_count = playlist_count
                                                    print(f"[INFO] Found {episode_count} episodes in playlist for season {season_number}")
                                                    break

                                        # If we didn't find episode count from playlists, try numberOfAssets
                                        if episode_count == 0 and 'numberOfAssets' in season_model:
                                            episode_count = season_model.get('numberOfAssets', 0)

                                        # If we still don't have episode count, try numberOfEpisodes
                                        if episode_count == 0 and 'numberOfEpisodes' in season_model:
                                            episode_count = season_model.get('numberOfEpisodes', 0)

                                        # If we found a better total_episodes count, update it
                                        if 'numberOfEpisodes' in season_model and season_model.get('numberOfEpisodes', 0) > 0:
                                            total_episodes = season_model.get('numberOfEpisodes', 0)
                                        elif 'totalNumberOfEpisodes' in season_model and season_model.get('totalNumberOfEpisodes', 0) > 0:
                                            total_episodes = season_model.get('totalNumberOfEpisodes', 0)
                            except Exception as e:
                                print(f"Error fetching detailed season data: {e}")

                            # If we still don't have episode count, check playlists in the season object
                            if episode_count == 0:
                                playlists = season.get('playlists', [])
                                for playlist in playlists:
                                    playlist_type = playlist.get('type', '')
                                    playlist_title = playlist.get('title', '')
                                    playlist_count = playlist.get('count', 0)

                                    if playlist_type == 'EPISODE' and playlist_title == 'Episodes':
                                        episode_count = playlist_count
                                        break

                            # If we still don't have episode count, use total episodes
                            if episode_count == 0:
                                episode_count = total_episodes

                            # If we still don't have total episodes, use episode count
                            if total_episodes == 0:
                                total_episodes = episode_count

                            # Format season name
                            season_name = f"Season {season_number}"
                            if episode_count > 0:
                                season_name += f" ({episode_count} episodes)"

                            seasons.append((season_id, season_number, season_name, episode_count, total_episodes))
                            print(f"[INFO] Added season: {season_name}, ID: {season_id}")

                        # Sort by season number
                        seasons.sort(key=lambda x: x[1])
                    else:
                        print("[INFO] No 'seasons' found in productModel")
                else:
                    print("[INFO] No 'productModel' found in response")

                return seasons
            else:
                print(f"[ERROR] Failed to get series details. Status code: {response.status_code}")
                return []
        except Exception as e:
            print(f"Error fetching series details: {e}")
            return []

    def get_season_episodes(self, season_id):
        """Get all episodes for a season."""
        # Using the same approach as in the original Shahid.py
        headers = {
            'browser_name': 'CHROME',
            'browser_version': '120.0.0.0',
            'shahid_os': 'WINDOWS',
            'language': 'EN',
            's-session': '',
            'token': self.token
        }

        # First, get detailed season information to get total episodes count
        params = {'request': '{"id":"' + str(season_id) + '"}', 'country': 'EG'}

        print(f"\n[INFO] Fetching season details for season_id: {season_id}")

        try:
            response = requests.get('https://api3.shahid.net/proxy/v2.1/product/id', params=params, headers=headers, proxies=self.proxies)
            total_episodes = 0

            if response.status_code == 200:
                response_data = response.json()
                episodes = []
                available_episodes = set()  # Track available episode numbers

                if 'productModel' in response_data:
                    product_model = response_data['productModel']

                    # Try to get total episodes count
                    if 'numberOfEpisodes' in product_model:
                        total_episodes = product_model.get('numberOfEpisodes', 0)
                        print(f"[INFO] Found numberOfEpisodes: {total_episodes}")
                    elif 'totalNumberOfEpisodes' in product_model:
                        total_episodes = product_model.get('totalNumberOfEpisodes', 0)
                        print(f"[INFO] Found totalNumberOfEpisodes: {total_episodes}")

                    # Find the episodes playlist
                    if 'playlists' in product_model:
                        for playlist in product_model['playlists']:
                            if playlist.get('type') == 'EPISODE':
                                playlist_id = playlist.get('id')
                                print(f"[INFO] Found episodes playlist with ID: {playlist_id}")

                                # Now we need to get the episodes from this playlist
                                # We'll use the same approach as in the original Shahid.py
                                page = 0
                                episode_display_number = 1
                                episode_numbers = []
                                temp_episodes = []

                                while True:
                                    playlist_params = {
                                        'request': '{"pageNumber":' + str(page) + ',"pageSize":30,"playListId":' + str(playlist_id) + ',"sorts":[{"order":"DESC","type":"SORTDATE"}]}',
                                        'country': 'SA'  # Using SA as in the original code
                                    }

                                    try:
                                        playlist_response = requests.get('https://api3.shahid.net/proxy/v2.1/product/playlist',
                                                                        headers=headers,
                                                                        params=playlist_params,
                                                                        proxies=self.proxies)

                                        if playlist_response.status_code != 200:
                                            break

                                        playlist_data = playlist_response.json()

                                        if playlist_data.get('responseCode') != 200:
                                            break

                                        if 'productList' not in playlist_data:
                                            break

                                        product_list = playlist_data['productList']

                                        if 'products' not in product_list or not product_list['products']:
                                            break

                                        print(f"[INFO] Found {len(product_list['products'])} episodes in page {page}")

                                        for episode in product_list['products']:
                                            episode_id = episode.get('id')
                                            original_episode_number = episode.get('number', episode_display_number)
                                            episode_title = episode.get('title', f'Episode {original_episode_number}')
                                            episode_name = f"Episode {original_episode_number}"

                                            # Check availability status
                                            availability_status = "Available"
                                            is_available = True

                                            # Check streamInfo if available
                                            if 'streamInfo' in episode and isinstance(episode['streamInfo'], dict):
                                                stream_info = episode['streamInfo']
                                                stream_state = stream_info.get('streamState', 'UNKNOWN')

                                                if stream_state == "COMING_SOON":
                                                    end_date = stream_info.get('endDate', None)
                                                    if end_date:
                                                        try:
                                                            # Parse the date
                                                            from datetime import datetime

                                                            # Convert to datetime object
                                                            # Try different date formats to handle both Z and +00:00 timezone formats
                                                            try:
                                                                # First try with Z format
                                                                end_date_obj = datetime.strptime(end_date, '%Y-%m-%dT%H:%M:%S.%fZ')
                                                            except ValueError:
                                                                try:
                                                                    # Then try with +00:00 format
                                                                    end_date_obj = datetime.strptime(end_date, '%Y-%m-%dT%H:%M:%S.%f%z')
                                                                except ValueError:
                                                                    # Try with other possible formats
                                                                    if '+' in end_date:
                                                                        # Handle +00:00 format manually
                                                                        date_part = end_date.split('+')[0]
                                                                        end_date_obj = datetime.strptime(date_part, '%Y-%m-%dT%H:%M:%S.%f')
                                                                    else:
                                                                        # If all else fails, just extract the date part
                                                                        date_part = end_date.split('T')[0]
                                                                        time_part = end_date.split('T')[1].split('.')[0]
                                                                        end_date_obj = datetime.strptime(f"{date_part}T{time_part}", '%Y-%m-%dT%H:%M:%S')

                                                            # Format the date
                                                            availability_status = f"Coming on {end_date_obj.strftime('%d/%m - %I:%M %p')}"
                                                            is_available = False
                                                        except Exception as e:
                                                            print(f"Error parsing date: {e}")
                                                            # Just display the raw date if we can't parse it
                                                            availability_status = f"Coming Soon ({end_date})"
                                                            is_available = False
                                                    else:
                                                        availability_status = "Coming Soon"
                                                        is_available = False
                                                elif stream_state != "VOD":
                                                    availability_status = stream_state
                                                    is_available = False

                                            # Only add if this episode number hasn't been added yet
                                            if original_episode_number not in available_episodes:
                                                temp_episodes.append((episode_id, original_episode_number, episode_name, episode_title, availability_status, is_available))
                                                episode_numbers.append(original_episode_number)
                                                available_episodes.add(original_episode_number)  # Mark as processed

                                            episode_display_number += 1

                                        # Go to next page
                                        page += 1

                                    except Exception as e:
                                        print(f"Error fetching playlist: {e}")
                                        break

                                # Sort episodes by episode number
                                if temp_episodes and episode_numbers:
                                    try:
                                        # Sort both lists based on episode numbers
                                        episode_numbers, temp_episodes = [list(t) for t in zip(*sorted(zip(episode_numbers, temp_episodes)))]
                                        episodes.extend(temp_episodes)
                                        # No need to add to available_episodes here since we already did it when adding to temp_episodes
                                    except Exception as e:
                                        print(f"Error sorting episodes: {e}")
                                        episodes.extend(temp_episodes)
                                else:
                                    print(f"[INFO] No episodes found for season {season_id}")
                    else:
                        print("[INFO] No 'playlists' found in productModel")
                else:
                    print("[INFO] No 'productModel' found in response")

                # If we have total_episodes and it's greater than available episodes,
                # add placeholder entries for missing episodes
                if total_episodes > len(episodes):
                    print(f"[INFO] Adding placeholders for missing episodes. Total: {total_episodes}, Available: {len(episodes)}")
                    for ep_num in range(1, total_episodes + 1):
                        if ep_num not in available_episodes:
                            episodes.append((None, ep_num, f"Episode {ep_num}", f"Episode {ep_num}", "Not yet available", False))

                # Sort again after adding placeholders
                episodes.sort(key=lambda x: x[1])

                return episodes
            else:
                print(f"[ERROR] Failed to get season details. Status code: {response.status_code}")
                return []
        except Exception as e:
            print(f"Error fetching season episodes: {e}")
            return []

    def get_episode_playout_url(self, episode_id):
        """Get episode playout URL."""
        print(f"[API] Getting playout URL for episode ID: {episode_id}")

        headers = {
            'browser_name': 'CHROME',
            'browser_version': '120.0.0.0',
            'shahid_os': 'WINDOWS',
            'language': 'EN',
            's-session': '',
            'token': self.token
        }

        playout_url = f'https://api3.shahid.net/proxy/v2.1/playout/new/url/{episode_id}?outputParameter=vmap&country=EG'
        print(f"[API] Requesting URL: {playout_url}")

        response = requests.get(playout_url, headers=headers, proxies=self.proxies)
        print(f"[API] Response status code: {response.status_code}")

        if response.status_code == 200:
            playout_data = response.json()
            print(f"[API] RESPUESTA COMPLETA DE LA API:")
            import json
            print(json.dumps(playout_data, indent=2))

            # Extract MPD URL and media information
            playout = playout_data.get('playout', {})
            print(f"[API] Playout data:")
            print(json.dumps(playout, indent=2))

            media_urls = playout.get('mediaUrls', [])
            print(f"[API] Media URLs:")
            print(json.dumps(media_urls, indent=2))

            # Process media URLs to get available codecs
            available_codecs = []
            mpd_urls = {}

            if media_urls:
                for media in media_urls:
                    encoding = media.get('encoding', 'Unknown')
                    url = media.get('url', '')
                    is_4k = media.get('4k', False)
                    is_hd = media.get('hd', False)

                    print(f"[API] Processing media URL: encoding={encoding}, is_4k={is_4k}, is_hd={is_hd}")
                    print(f"[API] URL: {url}")

                    # Fix URL if it contains "&." or "&" followed by another URL (common error in Shahid URLs)
                    if '&.' in url:
                        url = url.split('&.')[0]
                        print(f"[API] Fixed URL (removed &.): {url}")
                    elif '&' in url and 'edgenextcdn.net' in url.split('&')[1]:
                        url = url.split('&')[0]
                        print(f"[API] Fixed URL (removed duplicate URL): {url}")

                    if encoding not in [codec['name'] for codec in available_codecs]:
                        resolution_info = "4K" if is_4k else "HD" if is_hd else "SD"
                        available_codecs.append({
                            'name': encoding,
                            'resolution': resolution_info,
                            'url': url
                        })
                        mpd_urls[encoding] = url

            # If no media URLs found, try to get from the main URL
            if not available_codecs and 'url' in playout:
                mpd_url = playout.get('url', '')
                print(f"[API] No media URLs found, using main URL: {mpd_url}")

                # Fix URL if it contains "&." or "&" followed by another URL (common error in Shahid URLs)
                if '&.' in mpd_url:
                    mpd_url = mpd_url.split('&.')[0]
                    print(f"[API] Fixed URL (removed &.): {mpd_url}")
                elif '&' in mpd_url and 'edgenextcdn.net' in mpd_url.split('&')[1]:
                    mpd_url = mpd_url.split('&')[0]
                    print(f"[API] Fixed URL (removed duplicate URL): {mpd_url}")

                if mpd_url:
                    # Try to determine if it's H264 or H265
                    if 'H264' in mpd_url or 'h264' in mpd_url:
                        print(f"[API] Detected H264 in URL")
                        available_codecs.append({
                            'name': 'H264',
                            'resolution': 'HD',
                            'url': mpd_url
                        })
                        mpd_urls['H264'] = mpd_url

                        # Create H265 URL by replacing H264 with H265
                        h265_url = mpd_url.replace('H264', 'H265').replace('h264', 'h265')
                        h265_url = h265_url.replace('144-1080', '144-2160')
                        print(f"[API] Created H265 URL: {h265_url}")
                        available_codecs.append({
                            'name': 'H265',
                            'resolution': '4K',
                            'url': h265_url
                        })
                        mpd_urls['H265'] = h265_url

            # Extract audio and subtitle tracks
            tracks = playout.get('tracks', {})
            print(f"[API] Tracks from playout:")
            print(json.dumps(tracks, indent=2))

            audio_tracks = tracks.get('audios', [])
            subtitle_tracks = tracks.get('subtitles', [])

            print(f"[API] Raw audio tracks:")
            print(json.dumps(audio_tracks, indent=2))
            print(f"[API] Raw subtitle tracks:")
            print(json.dumps(subtitle_tracks, indent=2))

            # Process audio tracks
            processed_audio_tracks = []
            if isinstance(audio_tracks, list):
                for audio in audio_tracks:
                    if isinstance(audio, dict):
                        # Si es un diccionario, extraer código y nombre
                        lang_code = audio.get('code', '')
                        lang_name = audio.get('language', '')
                        print(f"[API] Processing audio track (dict): code={lang_code}, language={lang_name}")
                        processed_audio_tracks.append({
                            'code': lang_code,
                            'name': lang_name,
                            'display': f"{lang_name} ({lang_code})"
                        })
                    elif isinstance(audio, str):
                        # Si es una cadena, usar como código y buscar el nombre
                        lang_code = audio
                        from langcodes import Language
                        lang_name = Language.get(lang_code).display_name()
                        print(f"[API] Processing audio track (str): code={lang_code}, language={lang_name}")
                        processed_audio_tracks.append({
                            'code': lang_code,
                            'name': lang_name,
                            'display': f"{lang_name} ({lang_code})"
                        })

            # Process subtitle tracks
            processed_subtitle_tracks = []
            if isinstance(subtitle_tracks, list):
                for subtitle in subtitle_tracks:
                    if isinstance(subtitle, dict):
                        # Si es un diccionario, extraer código y nombre
                        lang_code = subtitle.get('code', '')
                        lang_name = subtitle.get('language', '')
                        print(f"[API] Processing subtitle track (dict): code={lang_code}, language={lang_name}")
                        processed_subtitle_tracks.append({
                            'code': lang_code,
                            'name': lang_name,
                            'display': f"{lang_name} ({lang_code})"
                        })
                    elif isinstance(subtitle, str):
                        # Si es una cadena, usar como código y buscar el nombre
                        lang_code = subtitle
                        from langcodes import Language
                        lang_name = Language.get(lang_code).display_name()
                        print(f"[API] Processing subtitle track (str): code={lang_code}, language={lang_name}")
                        processed_subtitle_tracks.append({
                            'code': lang_code,
                            'name': lang_name,
                            'display': f"{lang_name} ({lang_code})"
                        })

            # Add the extracted information to the playout data
            playout_data['available_codecs'] = available_codecs
            playout_data['mpd_urls'] = mpd_urls
            playout_data['audio_tracks'] = processed_audio_tracks
            playout_data['subtitle_tracks'] = processed_subtitle_tracks

            print(f"[API] Extracted MPD URLs:")
            print(json.dumps(mpd_urls, indent=2))
            print(f"[API] Extracted Audio Tracks:")
            print(json.dumps(processed_audio_tracks, indent=2))
            print(f"[API] Extracted Subtitle Tracks:")
            print(json.dumps(processed_subtitle_tracks, indent=2))

            return playout_data
        else:
            print(f"[ERROR] Failed to retrieve playout URL. Status code: {response.status_code}")
            return None

    def extract_poster_url(self, content_data):
        """Extract poster URL from content data."""
        if not content_data or not isinstance(content_data, dict):
            return None

        # Try to find poster in productModel.image.posterImage
        if 'productModel' in content_data:
            product = content_data['productModel']

            # Method 1: Check for image.posterImage
            if 'image' in product and 'posterImage' in product['image']:
                poster_url = product['image']['posterImage']
                # Remove query parameters if any
                if '?' in poster_url:
                    poster_url = poster_url.split('?')[0]
                # Try different image formats
                return f"{poster_url}?width=450&version=1&type=jpg&q=80"

            # Method 2: Check for thumbnails with imageType=POSTER
            if 'thumbnails' in product:
                for thumbnail in product['thumbnails']:
                    if thumbnail.get('imageType') == 'POSTER':
                        poster_url = thumbnail.get('url')
                        if poster_url:
                            # Remove query parameters if any
                            if '?' in poster_url:
                                poster_url = poster_url.split('?')[0]
                            return f"{poster_url}?width=450&version=1&type=jpg&q=80"

            # Method 3: Check for thumbnailImage in productModel
            if 'thumbnailImage' in product:
                poster_url = product['thumbnailImage']
                # Remove query parameters if any
                if '?' in poster_url:
                    poster_url = poster_url.split('?')[0]
                return f"{poster_url}?width=450&version=1&type=jpg&q=80"

            # Method 4: Check for playout.thumbnailImage
            if 'playout' in product and 'thumbnailImage' in product['playout']:
                poster_url = product['playout']['thumbnailImage']
                # Remove query parameters if any
                if '?' in poster_url:
                    poster_url = poster_url.split('?')[0]
                return f"{poster_url}?width=450&version=1&type=jpg&q=80"

            # Method 5: Check for thumbnails array
            if 'thumbnails' in product and isinstance(product['thumbnails'], list):
                for thumbnail in product['thumbnails']:
                    if 'url' in thumbnail:
                        poster_url = thumbnail['url']
                        # Remove query parameters if any
                        if '?' in poster_url:
                            poster_url = poster_url.split('?')[0]
                        return f"{poster_url}?width=450&version=1&type=jpg&q=80"

            # Method 6: Check for image.landscapeImage as fallback
            if 'image' in product and 'landscapeImage' in product['image']:
                poster_url = product['image']['landscapeImage']
                # Remove query parameters if any
                if '?' in poster_url:
                    poster_url = poster_url.split('?')[0]
                return f"{poster_url}?width=450&version=1&type=jpg&q=80"

        return None
