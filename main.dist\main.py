# ///////////////////////////////////////////////////////////////
#
# BY: WANDERSON M.PIMENTA
# PROJECT MADE WITH: Qt Designer and PySide6
# V: 1.0.0
#
# This project can be used freely for all uses, as long as they maintain the
# respective credits only in the Python scripts, any information in the visual
# interface (GUI) can be modified without any implication.
#
# There are limitations on Qt licenses if you want to use your products
# commercially, I recommend reading them on the official website:
# https://doc.qt.io/qtforpython/licenses.html
#
# ///////////////////////////////////////////////////////////////

import sys
import os
from pathlib import Path

# IMPORT / GUI AND MODULES AND WIDGETS
# ///////////////////////////////////////////////////////////////
from modules import *
from widgets import *

# IMPORT YANGO MODULES
# ///////////////////////////////////////////////////////////////
from modules.yango_api import YangoAPI
from modules.yango_ui import YangoUi
from modules.yango_drm import YangoDRM

os.environ["QT_FONT_DPI"] = "96" # FIX Problem for High DPI and Scale above 100%

# SET AS GLOBAL WIDGETS
# ///////////////////////////////////////////////////////////////
widgets = None

class MainWindow(QMainWindow):
    def __init__(self):
        QMainWindow.__init__(self)

        # SET AS GLOBAL WIDGETS
        # ///////////////////////////////////////////////////////////////
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        global widgets
        widgets = self.ui

        # USE CUSTOM TITLE BAR | USE AS "False" FOR MAC OR LINUX
        # ///////////////////////////////////////////////////////////////
        Settings.ENABLE_CUSTOM_TITLE_BAR = True

        # APP NAME
        # ///////////////////////////////////////////////////////////////
        title = "YANGO PLAY"
        description = "YANGO PLAY"
        # APPLY TEXTS
        self.setWindowTitle(title)
        if hasattr(widgets, 'titleLeftDescription'):
            widgets.titleLeftDescription.setText(description)

        # TOGGLE MENU
        # ///////////////////////////////////////////////////////////////
        widgets.toggleButton.clicked.connect(lambda: UIFunctions.toggleMenu(self, True))

        # SET UI DEFINITIONS
        # ///////////////////////////////////////////////////////////////
        UIFunctions.uiDefinitions(self)

        # QTableWidget PARAMETERS
        # ///////////////////////////////////////////////////////////////
        if hasattr(widgets, 'tableWidget'):
            widgets.tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # BUTTONS CLICK
        # ///////////////////////////////////////////////////////////////

        # LEFT MENUS
        widgets.btn_home.clicked.connect(self.buttonClick)
        widgets.btn_widgets.clicked.connect(self.buttonClick)
        widgets.btn_new.clicked.connect(self.buttonClick)
        widgets.btn_shahid.clicked.connect(self.buttonClick)
        widgets.btn_save.clicked.connect(self.buttonClick)

        # EXTRA LEFT BOX
        def openCloseLeftBox():
            UIFunctions.toggleLeftBox(self, True)
        if hasattr(widgets, 'toggleLeftBox'):
            widgets.toggleLeftBox.clicked.connect(openCloseLeftBox)
        if hasattr(widgets, 'extraCloseColumnBtn'):
            widgets.extraCloseColumnBtn.clicked.connect(openCloseLeftBox)

        # EXTRA RIGHT BOX
        def openCloseRightBox():
            UIFunctions.toggleRightBox(self, True)

        # SETTINGS BUTTON - Open Settings Dialog
        def openSettings():
            try:
                if hasattr(window, 'yango_ui') and window.yango_ui:
                    window.yango_ui.open_settings()
                else:
                    print("❌ YANGO UI not available")
            except Exception as e:
                print(f"❌ Error opening settings: {e}")

        if hasattr(widgets, 'settingsTopBtn'):
            widgets.settingsTopBtn.clicked.connect(openSettings)

        # SHOW APP
        # ///////////////////////////////////////////////////////////////
        self.show()

        # SET CUSTOM THEME
        # ///////////////////////////////////////////////////////////////
        useCustomTheme = False
        themeFile = "themes/py_dracula_dark.qss"

        # SET THEME AND HACKS
        if useCustomTheme:
            # LOAD AND APPLY STYLE
            UIFunctions.theme(self, themeFile, True)

            # SET HACKS
            AppFunctions.setThemeHack(self)

        # SET HOME PAGE AND SELECT MENU
        # ///////////////////////////////////////////////////////////////
        widgets.stackedWidget.setCurrentWidget(widgets.welcome_home)
        widgets.btn_home.setStyleSheet(UIFunctions.selectMenu(widgets.btn_home.styleSheet()))

        # Initialize YANGO modules
        self.yango_api = YangoAPI()
        self.yango_drm = YangoDRM()
        self.yango_ui = YangoUi(self)

        # Connect YANGO API signals
        self.yango_api.movie_found.connect(self.handle_movie_found)
        self.yango_api.series_found.connect(self.handle_series_found)
        self.yango_api.episodes_found.connect(self.handle_episodes_found)
        self.yango_api.streams_found.connect(self.handle_streams_found)
        self.yango_api.error_occurred.connect(self.handle_api_error)

        # Connect DRM signals
        self.yango_drm.keys_extracted.connect(self.handle_drm_keys)
        self.yango_drm.drm_error.connect(self.handle_drm_error)

        # Initialize OSN modules
        self.setup_osn_integration()

        # Initialize Shahid modules
        self.setup_shahid_integration()

        # Connect quick access buttons
        self.setup_quick_access_buttons()

        # Connect settings buttons
        self.setup_settings_buttons()

        # Create settings pages
        widgets.create_settings_pages()

        # Setup settings management
        self.setup_settings_management()

    # BUTTONS CLICK
    # Post here your functions for clicked buttons
    # ///////////////////////////////////////////////////////////////
    def buttonClick(self):
        # GET BUTTON CLICKED
        btn = self.sender()
        btnName = btn.objectName()

        # SHOW HOME PAGE (Welcome Page)
        if btnName == "btn_home":
            widgets.stackedWidget.setCurrentWidget(widgets.welcome_home)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))
            print("🏠 Switched to Home page")

        # SHOW YANGO PAGE (Main Application)
        if btnName == "btn_widgets":
            widgets.stackedWidget.setCurrentWidget(widgets.home)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))
            print("🎵 Switched to YANGO application")

        # SHOW OSN+ MODULE
        if btnName == "btn_new":
            self.show_osn_module()
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # SHOW SHAHID VIP MODULE
        if btnName == "btn_shahid":
            self.show_shahid_module()
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # SHOW SETTINGS PAGE
        if btnName == "btn_save":
            widgets.stackedWidget.setCurrentWidget(widgets.save_page)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))
            print("⚙️ Switched to Settings page")

        # PRINT BTN NAME
        print(f'Button "{btnName}" pressed!')

    def setup_quick_access_buttons(self):
        """Setup quick access buttons on home page"""
        try:
            # Connect YANGO quick button
            widgets.yango_quick_btn.clicked.connect(lambda: self.quick_access_yango())

            # Connect OSN+ quick button
            widgets.osn_quick_btn.clicked.connect(lambda: self.quick_access_osn())

            # Connect Shahid quick button (if exists)
            if hasattr(widgets, 'shahid_quick_btn'):
                widgets.shahid_quick_btn.clicked.connect(lambda: self.quick_access_shahid())

            # Connect Netflix quick button
            widgets.netflix_quick_btn.clicked.connect(lambda: self.quick_access_netflix())

            print("✅ Quick access buttons connected")

        except Exception as e:
            print(f"❌ Error setting up quick access buttons: {str(e)}")

    def quick_access_yango(self):
        """Quick access to YANGO from home page"""
        try:
            widgets.stackedWidget.setCurrentWidget(widgets.home)
            # Update sidebar selection
            UIFunctions.resetStyle(self, "btn_widgets")
            widgets.btn_widgets.setStyleSheet(UIFunctions.selectMenu(widgets.btn_widgets.styleSheet()))
            print("🎵 Quick access to YANGO")
        except Exception as e:
            print(f"❌ Error in quick access to YANGO: {str(e)}")

    def quick_access_osn(self):
        """Quick access to OSN+ from home page"""
        try:
            self.show_osn_module()
            # Update sidebar selection
            UIFunctions.resetStyle(self, "btn_new")
            widgets.btn_new.setStyleSheet(UIFunctions.selectMenu(widgets.btn_new.styleSheet()))
            print("🎬 Quick access to OSN+")
        except Exception as e:
            print(f"❌ Error in quick access to OSN+: {str(e)}")

    def quick_access_shahid(self):
        """Quick access to Shahid VIP from home page"""
        try:
            self.show_shahid_module()
            # Update sidebar selection
            UIFunctions.resetStyle(self, "btn_shahid")
            widgets.btn_shahid.setStyleSheet(UIFunctions.selectMenu(widgets.btn_shahid.styleSheet()))
            print("⭐ Quick access to Shahid VIP")
        except Exception as e:
            print(f"❌ Error in quick access to Shahid VIP: {str(e)}")

    def quick_access_netflix(self):
        """Quick access to Netflix from home page"""
        try:
            self.load_netflix_application()
            print("📺 Quick access to Netflix")
        except Exception as e:
            print(f"❌ Error in quick access to Netflix: {str(e)}")

    def setup_settings_buttons(self):
        """Setup settings page buttons"""
        try:
            # Create button group for exclusive selection
            self.settings_button_group = QButtonGroup()
            self.settings_button_group.addButton(widgets.yango_settings_btn, 0)
            self.settings_button_group.addButton(widgets.osn_settings_btn, 1)
            self.settings_button_group.addButton(widgets.shahid_settings_btn, 2)
            self.settings_button_group.addButton(widgets.netflix_settings_btn, 3)
            self.settings_button_group.addButton(widgets.general_settings_btn, 4)

            # Connect buttons to switch settings pages
            widgets.yango_settings_btn.clicked.connect(lambda: self.switch_settings_page(0))
            widgets.osn_settings_btn.clicked.connect(lambda: self.switch_settings_page(1))
            widgets.shahid_settings_btn.clicked.connect(lambda: self.switch_settings_page(2))
            widgets.netflix_settings_btn.clicked.connect(lambda: self.switch_settings_page(3))
            widgets.general_settings_btn.clicked.connect(lambda: self.switch_settings_page(4))

            # Set default page
            widgets.settings_content.setCurrentIndex(0)

            print("✅ Settings buttons connected")

        except Exception as e:
            print(f"❌ Error setting up settings buttons: {str(e)}")

    def switch_settings_page(self, page_index):
        """Switch to specific settings page"""
        try:
            widgets.settings_content.setCurrentIndex(page_index)

            page_names = ["YANGO", "OSN+", "Shahid VIP", "Netflix", "General"]
            print(f"⚙️ Switched to {page_names[page_index]} settings")

        except Exception as e:
            print(f"❌ Error switching settings page: {str(e)}")

    def setup_settings_management(self):
        """Setup settings management for all applications"""
        try:
            # Load settings managers for all apps
            from modules.yango_settings import YangoSettings
            from modules.OSN.settings_manager import SettingsManager as OSNSettings

            self.yango_settings = YangoSettings()
            self.osn_settings = OSNSettings()

            # Try to load Shahid settings
            try:
                from modules.Shahid.settings_manager import SettingsManager as ShahidSettings
                self.shahid_settings = ShahidSettings()
                print("✅ Shahid settings manager loaded")
            except ImportError:
                print("ℹ️ Shahid settings manager not available")
                self.shahid_settings = None

            # Connect save buttons in settings pages
            self.connect_settings_save_buttons()

            print("✅ Settings management setup completed")

        except Exception as e:
            print(f"❌ Error setting up settings management: {str(e)}")

    def connect_settings_save_buttons(self):
        """Connect save buttons in settings pages"""
        try:
            # This would connect actual save buttons when they're implemented
            # For now, we'll just print that the system is ready
            print("✅ Settings save buttons ready for connection")

        except Exception as e:
            print(f"❌ Error connecting settings save buttons: {str(e)}")

    def save_yango_settings(self, settings_data):
        """Save YANGO settings"""
        try:
            if hasattr(self, 'yango_settings'):
                # Update proxy settings
                if 'proxy' in settings_data:
                    proxy_config = settings_data['proxy']
                    self.yango_settings.current_settings['proxy'] = proxy_config
                    self.yango_settings.save_settings()
                    print("✅ YANGO proxy settings saved")

                # Update other settings as needed
                print("✅ YANGO settings saved successfully")

        except Exception as e:
            print(f"❌ Error saving YANGO settings: {str(e)}")

    def save_osn_settings(self, settings_data):
        """Save OSN+ settings"""
        try:
            if hasattr(self, 'osn_settings'):
                # Update proxy settings
                if 'proxy' in settings_data:
                    proxy_config = settings_data['proxy']
                    self.osn_settings.update_proxy_settings(proxy_config)
                    print("✅ OSN+ proxy settings saved")

                # Update other settings as needed
                print("✅ OSN+ settings saved successfully")

        except Exception as e:
            print(f"❌ Error saving OSN+ settings: {str(e)}")

    def save_shahid_settings(self, settings_data):
        """Save Shahid VIP settings"""
        try:
            if hasattr(self, 'shahid_settings') and self.shahid_settings:
                # Update proxy settings
                if 'proxy' in settings_data:
                    proxy_config = settings_data['proxy']
                    self.shahid_settings.update_proxy_settings(proxy_config)
                    print("✅ Shahid VIP proxy settings saved")

                # Update other settings as needed
                print("✅ Shahid VIP settings saved successfully")

        except Exception as e:
            print(f"❌ Error saving Shahid VIP settings: {str(e)}")

    def apply_global_proxy_settings(self, proxy_config):
        """Apply proxy settings to all applications"""
        try:
            # Apply to YANGO
            if hasattr(self, 'yango_settings'):
                self.yango_settings.current_settings['proxy'] = proxy_config
                self.yango_settings.save_settings()
                print("✅ Global proxy applied to YANGO")

            # Apply to OSN+
            if hasattr(self, 'osn_settings'):
                self.osn_settings.update_proxy_settings(proxy_config)
                print("✅ Global proxy applied to OSN+")

            # Apply to Shahid VIP
            if hasattr(self, 'shahid_settings') and self.shahid_settings:
                self.shahid_settings.update_proxy_settings(proxy_config)
                print("✅ Global proxy applied to Shahid VIP")

            print("✅ Global proxy settings applied to all applications")

        except Exception as e:
            print(f"❌ Error applying global proxy settings: {str(e)}")

    # OSN INTEGRATION SETUP
    # ///////////////////////////////////////////////////////////////


    def setup_osn_integration(self):
        """Setup OSN module integration"""
        try:
            # Import OSN original application
            from modules.OSN.osn_init import OSNWidget

            # Create OSN widget (original application)
            self.osn_widget = OSNWidget()

            # Add OSN widget to stacked widget
            widgets.stackedWidget.addWidget(self.osn_widget)

            print("✅ OSN original application integrated successfully")

        except Exception as e:
            print(f"❌ Error setting up OSN integration: {str(e)}")
            # Create placeholder if OSN fails to load
            self.create_osn_placeholder()

    def show_osn_module(self):
        """Show OSN module page"""
        try:
            if hasattr(self, 'osn_widget'):
                widgets.stackedWidget.setCurrentWidget(self.osn_widget)
                print("🎬 Switched to OSN+ module")
            else:
                print("❌ OSN+ module not available")

        except Exception as e:
            print(f"❌ Error showing OSN module: {str(e)}")

    def handle_osn_search(self, search_text):
        """Handle OSN search request"""
        try:
            print(f"🔍 OSN Search requested: {search_text}")

            # Update OSN widget status
            if hasattr(self, 'osn_widget'):
                self.osn_widget.update_status(f"Searching: {search_text}")

            # Extract content ID from URL or use direct ID
            content_id = search_text
            if "osnplus.com" in search_text:
                # Extract ID from URL
                parts = search_text.split("/")
                for part in parts:
                    if part.isdigit():
                        content_id = part
                        break
                    elif "-" in part:
                        # Handle format like "1234567-movie-name"
                        id_part = part.split("-")[0]
                        if id_part.isdigit():
                            content_id = id_part
                            break

            print(f"🔍 Extracted content ID: {content_id}")

            # Search using OSN API
            if hasattr(self, 'osn_api'):
                self.osn_api.search_content(content_id)
            else:
                if hasattr(self, 'osn_widget'):
                    self.osn_widget.show_error("OSN+ API not available")

        except Exception as e:
            print(f"❌ Error in OSN search: {str(e)}")
            if hasattr(self, 'osn_widget'):
                self.osn_widget.show_error(f"Search failed: {str(e)}")

    def handle_osn_content_found(self, content_data):
        """Handle OSN content found"""
        try:
            print(f"✅ OSN Content found: {content_data.get('type', 'unknown')}")

            # Update OSN widget with results
            if hasattr(self, 'osn_widget'):
                self.osn_widget.show_content_found(content_data)
                self.osn_widget.update_status("Content found successfully!")

        except Exception as e:
            print(f"❌ Error handling OSN content: {str(e)}")

    def handle_osn_error(self, error_message):
        """Handle OSN API error"""
        try:
            print(f"❌ OSN Error: {error_message}")

            # Update OSN widget with error
            if hasattr(self, 'osn_widget'):
                self.osn_widget.show_error(error_message)
                self.osn_widget.update_status(f"Error: {error_message}")

        except Exception as e:
            print(f"❌ Error handling OSN error: {str(e)}")

    # SHAHID INTEGRATION SETUP
    # ///////////////////////////////////////////////////////////////

    def setup_shahid_integration(self):
        """Setup Shahid VIP module integration"""
        try:
            # Import Shahid original application
            from modules.Shahid.shahid_init import ShahidWidget

            # Create Shahid widget (original application)
            self.shahid_widget = ShahidWidget()

            # Add Shahid widget to stacked widget
            widgets.stackedWidget.addWidget(self.shahid_widget)

            print("✅ Shahid VIP original application integrated successfully")

        except Exception as e:
            print(f"❌ Error setting up Shahid integration: {str(e)}")
            # Create placeholder if Shahid fails to load
            self.create_shahid_placeholder()

    def show_shahid_module(self):
        """Show Shahid VIP module page"""
        try:
            if hasattr(self, 'shahid_widget'):
                widgets.stackedWidget.setCurrentWidget(self.shahid_widget)
                print("⭐ Switched to Shahid VIP module")
            else:
                print("❌ Shahid VIP module not available")

        except Exception as e:
            print(f"❌ Error showing Shahid module: {str(e)}")

    def create_shahid_placeholder(self):
        """Create placeholder Shahid page when Shahid modules are not available"""
        try:
            from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel

            widgets.shahid_page = QWidget()
            widgets.shahid_page.setObjectName("shahid_page")

            layout = QVBoxLayout(widgets.shahid_page)

            placeholder_label = QLabel("⭐ Shahid VIP Integration\n\nShahid VIP modules not available.\nPlease check Shahid installation.")
            placeholder_label.setAlignment(Qt.AlignCenter)
            placeholder_label.setStyleSheet("""
                QLabel {
                    color: #f1c40f;
                    font-size: 18px;
                    padding: 40px;
                }
            """)
            layout.addWidget(placeholder_label)

            widgets.stackedWidget.addWidget(widgets.shahid_page)

        except Exception as e:
            print(f"❌ Error creating Shahid placeholder: {str(e)}")

    def load_netflix_application(self):
        """Load Netflix application (placeholder for future)"""
        try:
            # Check if Netflix page already exists
            if hasattr(widgets, 'netflix_main_widget') and widgets.netflix_main_widget:
                widgets.stackedWidget.setCurrentWidget(widgets.netflix_main_widget)
                print("📺 Switched to existing Netflix application")
                return

            # Create Netflix placeholder page
            self.create_netflix_placeholder()

        except Exception as e:
            print(f"❌ Error loading Netflix application: {str(e)}")

    def create_netflix_placeholder(self):
        """Create placeholder Netflix page"""
        try:
            from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton

            widgets.netflix_main_widget = QWidget()
            widgets.netflix_main_widget.setObjectName("netflix_main_widget")
            widgets.netflix_main_widget.setStyleSheet("background-color: transparent;")

            layout = QVBoxLayout(widgets.netflix_main_widget)
            layout.setContentsMargins(40, 40, 40, 40)
            layout.setSpacing(30)

            # Netflix logo/title
            title_label = QLabel("📺 NETFLIX DOWNLOADER")
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 36px;
                    font-weight: bold;
                    color: #e50914;
                    margin-bottom: 30px;
                }
            """)
            layout.addWidget(title_label)

            # Coming soon message
            message_label = QLabel("""
            🚧 Coming Soon! 🚧

            Netflix downloader is currently under development.

            Features planned:
            • Download Netflix movies and series
            • Multiple quality options
            • Subtitle support
            • Offline viewing capabilities

            Stay tuned for updates!
            """)
            message_label.setAlignment(Qt.AlignCenter)
            message_label.setWordWrap(True)
            message_label.setStyleSheet("""
                QLabel {
                    color: #f8f8f2;
                    font-size: 16px;
                    line-height: 1.8;
                    padding: 30px;
                    background-color: rgb(33, 37, 43);
                    border-radius: 12px;
                    border: 2px solid #e50914;
                }
            """)
            layout.addWidget(message_label)

            # Back button
            back_button = QPushButton("🔙 Back to YANGO")
            back_button.setMinimumHeight(50)
            back_button.setStyleSheet("""
                QPushButton {
                    background-color: #e50914;
                    color: #ffffff;
                    border: none;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: bold;
                    padding: 15px;
                }
                QPushButton:hover {
                    background-color: #f40612;
                }
                QPushButton:pressed {
                    background-color: #d40813;
                }
            """)
            back_button.clicked.connect(lambda: widgets.stackedWidget.setCurrentWidget(widgets.welcome_home))
            layout.addWidget(back_button)

            layout.addStretch()

            # Add to stacked widget
            widgets.stackedWidget.addWidget(widgets.netflix_main_widget)
            widgets.stackedWidget.setCurrentWidget(widgets.netflix_main_widget)

            print("📺 Netflix placeholder page created")

        except Exception as e:
            print(f"❌ Error creating Netflix placeholder: {str(e)}")

    def create_osn_page(self):
        """Create OSN page in the stacked widget"""
        try:
            from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QLineEdit, QPushButton, QHBoxLayout, QComboBox

            # Create OSN page widget
            widgets.osn_page = QWidget()
            widgets.osn_page.setObjectName("osn_page")
            widgets.osn_page.setStyleSheet("background-color: transparent;")

            # Main layout
            osn_layout = QVBoxLayout(widgets.osn_page)
            osn_layout.setContentsMargins(20, 20, 20, 20)
            osn_layout.setSpacing(20)

            # Header
            header_label = QLabel("🎬 OSN+ Downloader")
            header_label.setAlignment(Qt.AlignCenter)
            header_label.setStyleSheet("""
                QLabel {
                    font-size: 32px;
                    font-weight: bold;
                    color: #50fa7b;
                    margin-bottom: 20px;
                }
            """)
            osn_layout.addWidget(header_label)

            # Search section
            search_frame = QFrame()
            search_frame.setStyleSheet("""
                QFrame {
                    background-color: rgb(33, 37, 43);
                    border-radius: 8px;
                    padding: 20px;
                }
            """)
            search_layout = QVBoxLayout(search_frame)

            # Search title
            search_title = QLabel("Search OSN+ Content")
            search_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #50fa7b;")
            search_layout.addWidget(search_title)

            # Search input row
            search_row = QHBoxLayout()

            # URL input for OSN
            widgets.osn_url_input = QLineEdit()
            widgets.osn_url_input.setPlaceholderText("Enter OSN+ URL or content ID...")
            widgets.osn_url_input.setMinimumHeight(40)
            widgets.osn_url_input.setStyleSheet("""
                QLineEdit {
                    background-color: rgb(27, 29, 35);
                    border: 2px solid rgb(52, 59, 72);
                    border-radius: 6px;
                    padding: 8px;
                    color: #ffffff;
                    font-size: 14px;
                }
                QLineEdit:focus {
                    border: 2px solid #50fa7b;
                }
            """)
            search_row.addWidget(widgets.osn_url_input)

            # Search button for OSN
            widgets.osn_search_button = QPushButton("Search")
            widgets.osn_search_button.setMinimumSize(100, 40)
            widgets.osn_search_button.setStyleSheet("""
                QPushButton {
                    background-color: #50fa7b;
                    color: #282a36;
                    border: none;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #5af78e;
                }
                QPushButton:pressed {
                    background-color: #4ae66c;
                }
            """)
            search_row.addWidget(widgets.osn_search_button)

            search_layout.addLayout(search_row)
            osn_layout.addWidget(search_frame)

            # Content area placeholder
            content_label = QLabel("Enter OSN+ URL or content ID to start downloading")
            content_label.setAlignment(Qt.AlignCenter)
            content_label.setStyleSheet("""
                QLabel {
                    color: #6272a4;
                    font-size: 16px;
                    padding: 40px;
                }
            """)
            osn_layout.addWidget(content_label)

            # Add stretch
            osn_layout.addStretch()

            # Add OSN page to stacked widget
            widgets.stackedWidget.addWidget(widgets.osn_page)

            print("✅ OSN page created successfully")

        except Exception as e:
            print(f"❌ Error creating OSN page: {str(e)}")
            self.create_osn_placeholder()

    def create_osn_placeholder(self):
        """Create placeholder OSN page when OSN modules are not available"""
        try:
            from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel

            widgets.osn_page = QWidget()
            widgets.osn_page.setObjectName("osn_page")

            layout = QVBoxLayout(widgets.osn_page)

            placeholder_label = QLabel("🎬 OSN+ Integration\n\nOSN+ modules not available.\nPlease check OSN installation.")
            placeholder_label.setAlignment(Qt.AlignCenter)
            placeholder_label.setStyleSheet("""
                QLabel {
                    color: #ff5555;
                    font-size: 18px;
                    padding: 40px;
                }
            """)
            layout.addWidget(placeholder_label)

            widgets.stackedWidget.addWidget(widgets.osn_page)

        except Exception as e:
            print(f"❌ Error creating OSN placeholder: {str(e)}")

    # APPLICATION LAUNCHERS
    # ///////////////////////////////////////////////////////////////
    def launch_osn_application(self):
        """Launch OSN+ application"""
        try:
            import subprocess
            import os
            from pathlib import Path

            # Get OSN path (assuming it's in the same parent directory)
            current_dir = Path(__file__).parent
            osn_path = current_dir.parent / "OSN" / "main.py"

            if osn_path.exists():
                # Launch OSN application
                subprocess.Popen([
                    "python", str(osn_path)
                ], cwd=str(osn_path.parent))
                print("🎬 Launching OSN+ application...")

                # Show status message
                if hasattr(self, 'yango_ui') and self.yango_ui:
                    self.yango_ui.status_updated.emit("🎬 OSN+ application launched")
            else:
                print("❌ OSN+ application not found")
                if hasattr(self, 'yango_ui') and self.yango_ui:
                    self.yango_ui.show_message("Error", "OSN+ application not found")

        except Exception as e:
            print(f"❌ Error launching OSN+: {str(e)}")
            if hasattr(self, 'yango_ui') and self.yango_ui:
                self.yango_ui.show_message("Error", f"Failed to launch OSN+: {str(e)}")

    def launch_netflix_application(self):
        """Launch Netflix application (placeholder for future)"""
        try:
            print("📺 Netflix application not yet available")
            if hasattr(self, 'yango_ui') and self.yango_ui:
                self.yango_ui.show_message("Coming Soon", "Netflix downloader will be available in future updates!")

        except Exception as e:
            print(f"❌ Error with Netflix launcher: {str(e)}")





    # YANGO API HANDLERS
    # ///////////////////////////////////////////////////////////////
    def handle_movie_found(self, movie_data):
        """Handle when movie is found by YANGO API"""
        print(f"🎬 Movie found: {movie_data.get('title', 'Unknown')}")
        # Forward to UI handler
        if hasattr(self, 'yango_ui') and self.yango_ui:
            self.yango_ui.handle_movie_found(movie_data)

    def handle_series_found(self, series_data):
        """Handle when series is found by YANGO API"""
        print(f"📺 Series found: {series_data.get('title', 'Unknown')}")
        # Forward to UI handler
        if hasattr(self, 'yango_ui') and self.yango_ui:
            self.yango_ui.handle_series_found(series_data)

    def handle_episodes_found(self, episodes_data):
        """Handle when episodes are found by YANGO API"""
        print(f"📋 Episodes found: {len(episodes_data)} episodes")
        # Forward to UI handler
        if hasattr(self, 'yango_ui') and self.yango_ui:
            self.yango_ui.handle_episodes_found(episodes_data)

    def handle_streams_found(self, streams_data):
        """Handle when streams are found by YANGO API"""
        print(f"🎬 Streams found for content")
        # Forward to UI handler
        if hasattr(self, 'yango_ui') and self.yango_ui:
            self.yango_ui.handle_streams_found(streams_data)

    def handle_api_error(self, error_message):
        """Handle API errors"""
        print(f"❌ API Error: {error_message}")
        # Forward to UI handler
        if hasattr(self, 'yango_ui') and self.yango_ui:
            self.yango_ui.handle_api_error(error_message)

    def handle_drm_keys(self, keys):
        """Handle DRM keys extraction"""
        print(f"🔑 DRM keys extracted: {len(keys)} keys")
        if hasattr(self, 'yango_ui'):
            self.yango_ui.status_updated.emit("DRM keys extracted successfully")

    def handle_drm_error(self, error_message):
        """Handle DRM errors"""
        print(f"❌ DRM Error: {error_message}")
        if hasattr(self, 'yango_ui'):
            self.yango_ui.show_message("DRM Error", error_message)

    # RESIZE EVENTS
    # ///////////////////////////////////////////////////////////////
    def resizeEvent(self, event):
        # Update Size Grips
        UIFunctions.resize_grips(self)

    # MOUSE CLICK EVENTS
    # ///////////////////////////////////////////////////////////////
    def mousePressEvent(self, event):
        # SET DRAG POS WINDOW
        self.dragPos = event.globalPos()

        # PRINT MOUSE EVENTS
        if event.buttons() == Qt.LeftButton:
            print('Mouse click: LEFT CLICK')
        if event.buttons() == Qt.RightButton:
            print('Mouse click: RIGHT CLICK')

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon("YANGOTO.ico"))
    window = MainWindow()

    # Save data when application is about to quit
    def save_on_exit():
        try:
            if hasattr(window, 'yango_ui') and window.yango_ui:
                # Cleanup YANGO player
                window.yango_ui.cleanup_player()
                # Save recent URLs
                window.yango_ui.save_recent_urls_to_file()
                print("💾 Saved application data before exit")
        except Exception as e:
            print(f"❌ Error saving data on exit: {str(e)}")

    app.aboutToQuit.connect(save_on_exit)

    sys.exit(app.exec_())
