import os
import json
import requests
from PySide6.QtCore import QObject, Signal

class SettingsManager(QObject):
    """Class to manage application settings."""

    # Signals
    settings_changed = Signal(dict)  # Emitted when settings are changed

    def __init__(self):
        super().__init__()
        # Definir la ruta del archivo de configuración - usar config del directorio raíz
        # Ir 3 niveles arriba desde modules/Shahid/settings_manager.py para llegar al directorio raíz
        root_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        self.config_dir = os.path.join(root_dir, "config")
        self.settings_file = os.path.join(self.config_dir, "shahid_settings.json")

        # Definir configuraciones predeterminadas
        self.default_settings = {
            "token": "",
            "proxy": {
                "enabled": False,
                "type": "http",  # http, socks4, socks5
                "host": "",
                "port": "",
                "username": "",
                "password": ""
            },
            "ui": {
                "show_movies_tab": True,
                "show_series_tab": True,
                "show_settings_tab": True,
                "theme": "dark"
            },
            "download": {
                "default_quality": "720p",
                "default_audio": ["ar"],
                "default_subtitles": ["ar", "en"],
                "download_path": os.path.join(os.path.expanduser("~"), "Downloads", "Shahid")
            },
            "idm": {
                "enabled": True,
                "custom_path": "",
                "use_queue": True,
                "silent_mode": True
            }
        }

        # Crear el directorio de configuración si no existe
        os.makedirs(self.config_dir, exist_ok=True)
        print(f"[SETTINGS] Config directory: {self.config_dir}")
        print(f"[SETTINGS] Settings file: {self.settings_file}")

        # Verificar si el archivo de configuración existe
        if os.path.exists(self.settings_file):
            file_size = os.path.getsize(self.settings_file)
            print(f"[SETTINGS] Settings file exists: {file_size} bytes")

            # Si el archivo es muy grande o muy pequeño, podría estar corrupto
            if file_size > 10000 or file_size < 10:
                print(f"[SETTINGS] Settings file size suspicious: {file_size} bytes, recreating it")
                self.reset_settings()
        else:
            print(f"[SETTINGS] Settings file does not exist, will create it")

        # Cargar configuraciones
        self.settings = self.load_settings()

    def reset_settings(self):
        """Reset settings to default and recreate the settings file."""
        try:
            # Crear una copia de seguridad del archivo actual si existe
            if os.path.exists(self.settings_file):
                import time
                import shutil
                backup_file = f"{self.settings_file}.bak.{int(time.time())}"
                try:
                    shutil.copy2(self.settings_file, backup_file)
                    print(f"[SETTINGS] Backed up settings to {backup_file}")
                except Exception as backup_error:
                    print(f"[SETTINGS] Error backing up settings: {backup_error}")

                # Intentar eliminar el archivo actual
                try:
                    os.remove(self.settings_file)
                    print(f"[SETTINGS] Removed settings file for reset")
                except Exception as remove_error:
                    print(f"[SETTINGS] Error removing settings file: {remove_error}")
                    # Si no se puede eliminar, intentar sobrescribirlo

            # Crear una copia profunda de las configuraciones predeterminadas
            import copy
            default_settings_copy = copy.deepcopy(self.default_settings)

            # Guardar las configuraciones predeterminadas usando el método seguro
            success = self.save_settings(default_settings_copy)
            if not success:
                # Si falla, intentar guardar directamente
                try:
                    with open(self.settings_file, 'w', encoding='utf-8') as f:
                        json.dump(default_settings_copy, f, indent=4)
                    print(f"[SETTINGS] Reset settings using direct file write")
                    success = True
                except Exception as direct_write_error:
                    print(f"[SETTINGS] Error in direct write during reset: {direct_write_error}")
                    success = False

            # También eliminar el token del archivo token.txt
            try:
                script_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                token_path = os.path.join(script_dir, 'binaries', 'login', 'token.txt')
                if os.path.exists(token_path):
                    # Hacer una copia de seguridad del token
                    token_backup = f"{token_path}.bak.{int(time.time())}"
                    try:
                        shutil.copy2(token_path, token_backup)
                        print(f"[SETTINGS] Backed up token to {token_backup}")
                    except Exception as token_backup_error:
                        print(f"[SETTINGS] Error backing up token: {token_backup_error}")

                    # Eliminar el archivo de token
                    try:
                        os.remove(token_path)
                        print(f"[SETTINGS] Removed token file")
                    except Exception as token_remove_error:
                        print(f"[SETTINGS] Error removing token file: {token_remove_error}")

                # Actualizar el token en la configuración
                self.settings["token"] = ""
            except Exception as token_error:
                print(f"[SETTINGS] Error handling token during reset: {token_error}")

            # Actualizar las configuraciones en memoria
            self.settings = copy.deepcopy(default_settings_copy)

            print(f"[SETTINGS] Reset settings to default values")
            return success
        except Exception as e:
            print(f"[SETTINGS] Error resetting settings: {e}")
            return False

    def load_settings(self):
        """Load settings from file or create default if not exists."""
        try:
            # Create config directory if it doesn't exist
            os.makedirs(os.path.dirname(self.settings_file), exist_ok=True)

            # If settings file exists, load it
            if os.path.exists(self.settings_file):
                try:
                    # Check if file is empty
                    if os.path.getsize(self.settings_file) == 0:
                        print(f"[SETTINGS] Settings file is empty, using defaults")
                        self.save_settings(self.default_settings)
                        return self.default_settings.copy()

                    # Try to load the file
                    with open(self.settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)

                    # Verificar y corregir la estructura de datos
                    fixed_settings = self.validate_settings_structure(settings)

                    # Save if settings were fixed
                    if fixed_settings != settings:
                        print("[SETTINGS] Fixed invalid settings structure")
                        self.save_settings(fixed_settings)

                    print(f"[SETTINGS] Successfully loaded settings from {self.settings_file}")
                    return fixed_settings
                except json.JSONDecodeError as json_error:
                    print(f"[SETTINGS] Error decoding JSON: {json_error}")

                    # Intentar reparar el archivo JSON
                    try:
                        print("[SETTINGS] Attempting to repair JSON file")
                        with open(self.settings_file, 'r', encoding='utf-8') as f:
                            content = f.read()

                        # Buscar y corregir problemas comunes en el JSON
                        # 1. Corregir pares invertidos como "true": "show_settings_tab"
                        content = self.fix_inverted_json_pairs(content)

                        # Intentar cargar el JSON reparado
                        try:
                            repaired_settings = json.loads(content)
                            print("[SETTINGS] Successfully repaired JSON file")

                            # Guardar el JSON reparado
                            with open(self.settings_file, 'w', encoding='utf-8') as f:
                                json.dump(repaired_settings, f, indent=4)

                            return self.validate_settings_structure(repaired_settings)
                        except json.JSONDecodeError:
                            print("[SETTINGS] Failed to repair JSON file")
                    except Exception as repair_error:
                        print(f"[SETTINGS] Error repairing JSON: {repair_error}")

                    # Si la reparación falla, intentar cargar desde el backup
                    backup_file = os.path.join(os.path.dirname(self.settings_file), "settings_backup.json")
                    if os.path.exists(backup_file):
                        try:
                            with open(backup_file, 'r', encoding='utf-8') as f:
                                backup_settings = json.load(f)
                            print(f"[SETTINGS] Loaded settings from backup file")
                            return self.validate_settings_structure(backup_settings)
                        except Exception as backup_error:
                            print(f"[SETTINGS] Error loading backup: {backup_error}")

                    # Si todo falla, resetear a los valores predeterminados
                    print(f"[SETTINGS] Resetting to default settings")
                    self.reset_settings()
                    return self.default_settings.copy()
            else:
                # Create new settings file with defaults
                print(f"[SETTINGS] Settings file not found, creating new one")
                self.save_settings(self.default_settings)
                return self.default_settings.copy()
        except Exception as e:
            print(f"[SETTINGS] Error loading settings: {e}")
            return self.default_settings.copy()

    def validate_settings_structure(self, settings):
        """Validate and fix settings structure."""
        # Create a copy of settings to avoid modifying the original
        fixed_settings = settings.copy()

        # Check each key in default settings
        for key, default_value in self.default_settings.items():
            # If key doesn't exist in settings, add it
            if key not in fixed_settings:
                fixed_settings[key] = default_value.copy() if isinstance(default_value, dict) else default_value
                print(f"[SETTINGS] Added missing key: {key}")
                continue

            # If value should be a dict but isn't, replace it
            if isinstance(default_value, dict):
                if not isinstance(fixed_settings[key], dict):
                    print(f"[SETTINGS] Fixed {key}: was {type(fixed_settings[key])}, should be dict")
                    fixed_settings[key] = default_value.copy()
                else:
                    # Limpiar completamente la sección para eliminar claves inválidas
                    # Crear una copia limpia con solo las claves válidas
                    clean_section = {}

                    # Copiar solo las claves válidas del default_value
                    for subkey, subvalue in default_value.items():
                        # Si la subclave existe en la configuración actual, usar ese valor
                        if subkey in fixed_settings[key]:
                            # Verificar que el tipo de dato sea correcto
                            if isinstance(subvalue, bool) and not isinstance(fixed_settings[key][subkey], bool):
                                # Convertir strings "true"/"false" a booleanos
                                if isinstance(fixed_settings[key][subkey], str):
                                    if fixed_settings[key][subkey].lower() == "true":
                                        clean_section[subkey] = True
                                    elif fixed_settings[key][subkey].lower() == "false":
                                        clean_section[subkey] = False
                                    else:
                                        clean_section[subkey] = subvalue
                                else:
                                    clean_section[subkey] = subvalue
                            elif isinstance(subvalue, str) and not isinstance(fixed_settings[key][subkey], str):
                                # Convertir a string si es posible
                                try:
                                    clean_section[subkey] = str(fixed_settings[key][subkey])
                                except:
                                    clean_section[subkey] = subvalue
                            elif isinstance(subvalue, list) and not isinstance(fixed_settings[key][subkey], list):
                                # Usar el valor predeterminado para listas
                                clean_section[subkey] = subvalue.copy()
                            else:
                                # Usar el valor existente si el tipo es correcto
                                clean_section[subkey] = fixed_settings[key][subkey]
                        else:
                            # Si la subclave no existe, usar el valor predeterminado
                            if isinstance(subvalue, dict) or isinstance(subvalue, list):
                                clean_section[subkey] = subvalue.copy()
                            else:
                                clean_section[subkey] = subvalue

                    # Reemplazar la sección completa con la versión limpia
                    fixed_settings[key] = clean_section
                    print(f"[SETTINGS] Cleaned {key} section to ensure valid structure")

        # Special handling for UI section
        if "ui" in fixed_settings and isinstance(fixed_settings["ui"], dict):
            ui_settings = fixed_settings["ui"]

            # Ensure show_movies_tab is a boolean
            if "show_movies_tab" in ui_settings and not isinstance(ui_settings["show_movies_tab"], bool):
                if isinstance(ui_settings["show_movies_tab"], str) and ui_settings["show_movies_tab"].lower() == "true":
                    ui_settings["show_movies_tab"] = True
                elif isinstance(ui_settings["show_movies_tab"], str) and ui_settings["show_movies_tab"].lower() == "false":
                    ui_settings["show_movies_tab"] = False
                else:
                    ui_settings["show_movies_tab"] = True
                print("[SETTINGS] Fixed ui.show_movies_tab to be boolean")

            # Ensure show_series_tab is a boolean
            if "show_series_tab" in ui_settings and not isinstance(ui_settings["show_series_tab"], bool):
                if isinstance(ui_settings["show_series_tab"], str) and ui_settings["show_series_tab"].lower() == "true":
                    ui_settings["show_series_tab"] = True
                elif isinstance(ui_settings["show_series_tab"], str) and ui_settings["show_series_tab"].lower() == "false":
                    ui_settings["show_series_tab"] = False
                else:
                    ui_settings["show_series_tab"] = True
                print("[SETTINGS] Fixed ui.show_series_tab to be boolean")

            # Ensure show_settings_tab is a boolean
            if "show_settings_tab" in ui_settings and not isinstance(ui_settings["show_settings_tab"], bool):
                if isinstance(ui_settings["show_settings_tab"], str) and ui_settings["show_settings_tab"].lower() == "true":
                    ui_settings["show_settings_tab"] = True
                elif isinstance(ui_settings["show_settings_tab"], str) and ui_settings["show_settings_tab"].lower() == "false":
                    ui_settings["show_settings_tab"] = False
                else:
                    ui_settings["show_settings_tab"] = True
                print("[SETTINGS] Fixed ui.show_settings_tab to be boolean")

            # Ensure theme is a string
            if "theme" in ui_settings and not isinstance(ui_settings["theme"], str):
                ui_settings["theme"] = "dark"
                print("[SETTINGS] Fixed ui.theme to be string 'dark'")

        return fixed_settings

    def save_settings(self, settings=None):
        """Save settings to file."""
        if settings is None:
            settings = self.settings

        try:
            # Create config directory if it doesn't exist
            os.makedirs(os.path.dirname(self.settings_file), exist_ok=True)

            # Validate settings structure before saving
            validated_settings = self.validate_settings_structure(settings)

            # Crear una copia de seguridad antes de guardar
            try:
                if os.path.exists(self.settings_file) and os.path.getsize(self.settings_file) > 0:
                    import time
                    import shutil
                    backup_file = f"{self.settings_file}.bak"
                    shutil.copy2(self.settings_file, backup_file)
                    print(f"[SETTINGS] Created backup at {backup_file}")
            except Exception as backup_error:
                print(f"[SETTINGS] Error creating backup: {backup_error}")

            # Save settings to file with error handling
            try:
                # Usar un archivo temporal primero para evitar corrupción
                import tempfile
                temp_file = os.path.join(tempfile.gettempdir(), f"shahid_settings_temp_{int(time.time())}.json")

                # Escribir en el archivo temporal
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(validated_settings, f, indent=4)

                # Verificar que el archivo temporal se creó correctamente
                if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
                    # Copiar el archivo temporal al archivo final
                    shutil.copy2(temp_file, self.settings_file)

                    # Eliminar el archivo temporal
                    try:
                        os.remove(temp_file)
                    except:
                        pass

                    # Verificar el archivo final
                    if os.path.exists(self.settings_file):
                        file_size = os.path.getsize(self.settings_file)
                        if file_size > 0:
                            print(f"[SETTINGS] Settings saved to {self.settings_file} ({file_size} bytes)")
                        else:
                            print(f"[SETTINGS] Warning: File is empty at {self.settings_file}")
                            raise Exception("File is empty after saving")
                    else:
                        print(f"[SETTINGS] Warning: File not created at {self.settings_file}")
                        raise Exception("File not created after saving")
                else:
                    print(f"[SETTINGS] Error: Temporary file not created or empty")
                    raise Exception("Temporary file not created or empty")

            except Exception as write_error:
                print(f"[SETTINGS] Error writing settings file: {write_error}")

                # Intentar restaurar desde la copia de seguridad si existe
                backup_file = f"{self.settings_file}.bak"
                if os.path.exists(backup_file) and os.path.getsize(backup_file) > 0:
                    try:
                        shutil.copy2(backup_file, self.settings_file)
                        print(f"[SETTINGS] Restored from backup after save error")
                    except Exception as restore_error:
                        print(f"[SETTINGS] Error restoring from backup: {restore_error}")

                # Si todo falla, intentar guardar directamente
                try:
                    with open(self.settings_file, 'w', encoding='utf-8') as f:
                        json.dump(validated_settings, f, indent=4)
                    print(f"[SETTINGS] Settings saved directly after temp file failure")
                except Exception as direct_error:
                    print(f"[SETTINGS] Error in direct save: {direct_error}")
                    return False

            # Update current settings
            self.settings = validated_settings

            # Emit signal
            self.settings_changed.emit(validated_settings)

            return True
        except Exception as e:
            print(f"[SETTINGS] Error saving settings: {e}")
            # Try to save to a backup location
            try:
                backup_file = os.path.join(os.path.dirname(self.settings_file), "settings_backup.json")
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, indent=4)
                print(f"[SETTINGS] Settings saved to backup file: {backup_file}")
            except Exception as backup_error:
                print(f"[SETTINGS] Error saving to backup file: {backup_error}")
            return False

    def get_setting(self, key, subkey=None):
        """Get a setting value."""
        try:
            # Verificar si la clave existe
            if key not in self.settings:
                # Si no existe, usar el valor predeterminado
                if subkey:
                    return self.default_settings.get(key, {}).get(subkey)
                return self.default_settings.get(key)

            # Si la clave existe pero no es un diccionario y se solicita una subclave
            if subkey and not isinstance(self.settings[key], dict):
                print(f"[SETTINGS] Warning: {key} is not a dictionary but a {type(self.settings[key])}. Using default value.")
                # Corregir la estructura
                self.settings[key] = self.default_settings.get(key, {}).copy()
                self.save_settings()
                return self.default_settings.get(key, {}).get(subkey)

            # Obtener el valor normalmente
            if subkey:
                # Si la subclave no existe, usar el valor predeterminado
                if subkey not in self.settings[key]:
                    return self.default_settings.get(key, {}).get(subkey)
                return self.settings[key][subkey]

            return self.settings[key]
        except Exception as e:
            print(f"[SETTINGS] Error getting setting {key}.{subkey}: {e}")
            # En caso de error, devolver el valor predeterminado
            if subkey:
                return self.default_settings.get(key, {}).get(subkey)
            return self.default_settings.get(key)

    def set_setting(self, key, value, subkey=None, save_immediately=True):
        """Set a setting value.

        Args:
            key: The main setting key
            value: The value to set
            subkey: Optional subkey for nested settings
            save_immediately: Whether to save settings to disk immediately (default: True)
        """
        try:
            # Asegurarse de que la estructura de configuración sea correcta
            if key not in self.settings:
                if subkey:
                    self.settings[key] = {}
                else:
                    self.settings[key] = value
                    print(f"[SETTINGS] Set {key} = {value}")
                    if save_immediately:
                        self.save_settings()
                    return True

            # Si la clave existe pero no es un diccionario y se necesita una subclave
            if subkey:
                if not isinstance(self.settings[key], dict):
                    print(f"[SETTINGS] Converting {key} from {type(self.settings[key])} to dict")
                    self.settings[key] = {}

                # Verificar si el valor ya está establecido para evitar guardados innecesarios
                if subkey in self.settings[key] and self.settings[key][subkey] == value:
                    # El valor ya está establecido, no es necesario hacer nada
                    return True

                # Establecer el nuevo valor
                self.settings[key][subkey] = value
                print(f"[SETTINGS] Set {key}.{subkey} = {value}")
            else:
                # Verificar si el valor ya está establecido para evitar guardados innecesarios
                if self.settings[key] == value:
                    # El valor ya está establecido, no es necesario hacer nada
                    return True

                # Establecer el nuevo valor
                self.settings[key] = value
                print(f"[SETTINGS] Set {key} = {value}")

            # Save settings if requested
            if save_immediately:
                self.save_settings()
            return True
        except Exception as e:
            print(f"[SETTINGS] Error setting {key}.{subkey} to {value}: {e}")
            return False

    def set_settings_batch(self, settings_dict):
        """Set multiple settings at once and save only once at the end.

        Args:
            settings_dict: Dictionary with format {(key, subkey): value} or {key: value}
        """
        try:
            success = True
            for key_info, value in settings_dict.items():
                if isinstance(key_info, tuple) and len(key_info) == 2:
                    key, subkey = key_info
                    if not self.set_setting(key, value, subkey, save_immediately=False):
                        success = False
                else:
                    key = key_info
                    if not self.set_setting(key, value, save_immediately=False):
                        success = False

            # Save all settings at once
            if success:
                self.save_settings()
            return success
        except Exception as e:
            print(f"[SETTINGS] Error in batch setting: {e}")
            return False

    def get_proxy_dict(self):
        """Get proxy settings as a dictionary for requests."""
        if not self.get_setting('proxy', 'enabled'):
            return None

        proxy_type = self.get_setting('proxy', 'type')
        host = self.get_setting('proxy', 'host')
        port = self.get_setting('proxy', 'port')
        username = self.get_setting('proxy', 'username')
        password = self.get_setting('proxy', 'password')

        if not host or not port:
            return None

        # Build proxy URL
        proxy_url = f"{proxy_type}://"
        if username and password:
            proxy_url += f"{username}:{password}@"
        proxy_url += f"{host}:{port}"

        return {
            "http": proxy_url,
            "https": proxy_url
        }

    def fix_inverted_json_pairs(self, content):
        """Fix inverted JSON pairs like "true": "show_settings_tab" to "show_settings_tab": true."""
        import re

        # Lista de valores que podrían estar invertidos
        value_keywords = [
            "true", "false",                                # Booleanos
            "dark", "light",                                # Temas
            "720p", "1080p", "480p", "360p", "2160p",       # Calidades
            "default_quality", "download_path", "theme",    # Claves de configuración
            "show_movies_tab", "show_series_tab", "show_settings_tab"  # Claves de UI
        ]

        # Buscar patrones como "value": "key" y corregirlos a "key": value
        for value in value_keywords:
            # Caso 1: "true": "show_settings_tab" -> "show_settings_tab": true
            if value in ["true", "false"]:
                # Convertir "true"/"false" (como clave) a true/false (como valor)
                pattern = f'"{value}"\\s*:\\s*"([^"]+)"'
                replacement = r'"\1": ' + value
                content = re.sub(pattern, replacement, content)

            # Caso 2: "720p": "default_quality" -> "default_quality": "720p"
            elif value in ["720p", "1080p", "480p", "360p", "2160p"]:
                pattern = f'"{value}"\\s*:\\s*"([^"]+)"'
                replacement = r'"\1": "' + value + '"'
                content = re.sub(pattern, replacement, content)

            # Caso 3: "dark": "theme" -> "theme": "dark"
            elif value in ["dark", "light"]:
                pattern = f'"{value}"\\s*:\\s*"([^"]+)"'
                replacement = r'"\1": "' + value + '"'
                content = re.sub(pattern, replacement, content)

            # Caso 4: "C:\\path": "download_path" -> "download_path": "C:\\path"
            elif value in ["default_quality", "download_path", "theme", "show_movies_tab", "show_series_tab", "show_settings_tab"]:
                # Buscar el valor como clave y la clave como valor
                pattern = f'"([^"]+)"\\s*:\\s*"{value}"'
                replacement = f'"{value}": "\\1"'
                content = re.sub(pattern, replacement, content)

        return content

    def test_proxy(self):
        """Test proxy connection."""
        proxies = self.get_proxy_dict()
        if not proxies:
            return False, "Proxy is not enabled or configured properly"

        try:
            response = requests.get("https://www.google.com", proxies=proxies, timeout=10)
            if response.status_code == 200:
                return True, "Proxy connection successful"
            else:
                return False, f"Proxy connection failed with status code: {response.status_code}"
        except Exception as e:
            return False, f"Proxy connection failed: {str(e)}"
