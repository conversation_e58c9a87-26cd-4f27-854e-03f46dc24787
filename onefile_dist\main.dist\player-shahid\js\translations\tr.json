{"advertising": {"admessage": "Bu reklam xx saniye sonra bitecek", "cuetext": "<PERSON><PERSON><PERSON>", "displayHeading": "<PERSON><PERSON><PERSON>", "loadingAd": "<PERSON><PERSON><PERSON> yü<PERSON>", "podmessage": "__AD_POD_CURRENT__ re<PERSON><PERSON>dan __AD_POD_LENGTH__.", "skipmessage": "xx saniye içerisinde reklamı geç", "skiptext": "Geç"}, "airplay": "AirPlay", "audioTracks": "<PERSON><PERSON><PERSON><PERSON>", "auto": "Otomatik", "buffer": "Yükleniyor", "cast": "Chromecast", "cc": "İşitme Engelliler İçin Alt Yazılar (Closed Captions)", "close": "Ka<PERSON><PERSON>", "errors": {"badConnection": "İnternet bağlantınızdaki bir sorundan dolayı bu video oynatılamıyor.", "cantLoadPlayer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, video oynatıcı yüklemede başar<PERSON>s<PERSON>z oldu.", "cantPlayInBrowser": "Video bu tarayı<PERSON>ıda oynatılamıyor.", "cantPlayVideo": "Bu video dosyası oynatılamıyor.", "errorCode": "<PERSON><PERSON>", "liveStreamDown": "Canlı yayın ya kesildi ya da sona erdi.", "protectedContent": "<PERSON><PERSON><PERSON>e erişimde bir sorun var.", "technicalError": "Bir teknik hatadan dolayı bu video oynatılamıyor."}, "exitFullscreen": "Tam Ekrandan Çık", "fullscreen": "<PERSON>", "hd": "<PERSON><PERSON>", "liveBroadcast": "Canlı", "logo": "Logo", "mute": "<PERSON><PERSON>", "next": "<PERSON><PERSON>", "nextUp": "<PERSON><PERSON><PERSON>", "notLive": "Canlı Değil", "off": "Ka<PERSON><PERSON>", "pause": "<PERSON><PERSON><PERSON>", "pipIcon": "Resim İçinde Resim", "play": "<PERSON><PERSON><PERSON>", "playback": "<PERSON><PERSON><PERSON>", "playbackRates": "Yeniden Oynatma Hızları", "player": "Video Oynatıcı", "poweredBy": "Powered by", "prev": "<PERSON><PERSON><PERSON>", "related": {"autoplaymessage": "Sonraki xx saniye içerisinde", "heading": "<PERSON>ha <PERSON>"}, "replay": "<PERSON><PERSON><PERSON>", "rewind": "10 Saniye Geri Sar", "settings": "<PERSON><PERSON><PERSON>", "sharing": {"copied": "Kopyalandı", "email": "E-posta", "embed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heading": "Paylaş", "link": "Bağlantı"}, "slider": "<PERSON>ynat<PERSON>", "stop": "<PERSON><PERSON><PERSON>", "unmute": "Sesi Aç", "videoInfo": "Bu Video Hakkında", "volume": "Ses", "volumeSlider": "<PERSON><PERSON>", "shortcuts": {"playPause": "Oynat/Du<PERSON>lat", "volumeToggle": "Sesi Kapat/Sesi Aç", "fullscreenToggle": "Tam Ekran/Tam E<PERSON>ndan Çık", "seekPercent": "Sar %", "keyboardShortcuts": "<PERSON><PERSON>", "increaseVolume": "<PERSON><PERSON>", "decreaseVolume": "<PERSON><PERSON>", "seekForward": "<PERSON><PERSON>", "seekBackward": "<PERSON><PERSON>", "spacebar": "Aralık <PERSON>", "captionsToggle": "Alt Yazı Açık/Kapalı"}, "captionsStyles": {"subtitleSettings": "Altyazı Ayarları", "color": "Font <PERSON>", "fontOpacity": "Font Parlaklığı", "userFontScale": "Font Büyüklüğü", "fontFamily": "Font Türü", "edgeStyle": "<PERSON><PERSON><PERSON>", "backgroundColor": "Arka Plan Rengi", "backgroundOpacity": "Arka Plan Parlaklığı", "windowColor": "<PERSON><PERSON><PERSON>", "windowOpacity": "<PERSON><PERSON><PERSON> Parlaklığı", "white": "<PERSON><PERSON>", "black": "Siyah", "red": "Kırmızı", "green": "<PERSON><PERSON><PERSON>", "blue": "<PERSON><PERSON>", "yellow": "Sarı", "magenta": "Kızılımsı Mor", "cyan": "<PERSON>", "none": "Hiç<PERSON>i", "raised": "Yukarıya Kaldırılmış", "depressed": "Aşağıya Bastırılmış", "uniform": "Üniform", "dropShadow": "Kabartı Gölgesi"}, "disabled": "Devredışı", "enabled": "<PERSON><PERSON><PERSON>", "reset": "Sıfırla"}