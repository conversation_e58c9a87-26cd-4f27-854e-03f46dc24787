#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Simplified OSN API for integration into YANGO application
"""

import requests
import json
import os
from urllib.parse import unquote
from PySide6.QtCore import QObject, Signal

class OSNApiSimple(QObject):
    # Signals for UI updates
    login_status_changed = Signal(bool, str)
    content_found = Signal(dict)
    episodes_found = Signal(dict)
    error_occurred = Signal(str)

    def __init__(self):
        super().__init__()
        self.saved_access_token = None
        self.base_url = "https://api.osnplus.com"
        self.auth_url = "https://koussa-osn.anghami.com"
        
        # Try to load token from OSN directory
        self.load_token_from_osn()

    def load_token_from_osn(self):
        """Load access token from OSN directory if available"""
        try:
            # Try to load from OSN directory
            osn_token_path = "../OSN/refresh_token.txt"
            if os.path.exists(osn_token_path):
                with open(osn_token_path, "r") as f:
                    content = f.read()
                    if "accessToken:" in content:
                        self.saved_access_token = content.split("accessToken:")[-1].strip()
                        print("✅ Loaded OSN access token from OSN directory")
                        return
            
            # Try to load from OSN cookies
            osn_cookies_path = "../OSN/cookies.txt"
            if os.path.exists(osn_cookies_path):
                refresh_token = self.load_refresh_token_from_cookies(osn_cookies_path)
                if refresh_token:
                    self.saved_access_token = self.refresh_access_token_simple(refresh_token)
                    print("✅ Generated OSN access token from cookies")
                    return
                    
            print("⚠️ No OSN credentials found")
            
        except Exception as e:
            print(f"❌ Error loading OSN credentials: {str(e)}")

    def load_refresh_token_from_cookies(self, file_path):
        """Load refreshToken from cookies file"""
        try:
            with open(file_path, "r") as file:
                lines = file.readlines()
                for line in lines:
                    if "auth" in line and not line.startswith("#"):
                        try:
                            parts = line.strip().split("\t")
                            if len(parts) >= 7:
                                auth_cookie = parts[-1].strip()
                                decoded_auth = unquote(auth_cookie)
                                
                                try:
                                    auth_data = json.loads(decoded_auth)
                                except json.JSONDecodeError:
                                    decoded_auth = unquote(decoded_auth)
                                    auth_data = json.loads(decoded_auth)
                                
                                refresh_token = auth_data.get("refreshToken")
                                if refresh_token:
                                    return refresh_token
                        except:
                            continue
            return None
        except Exception as e:
            print(f"❌ Error reading cookies: {str(e)}")
            return None

    def refresh_access_token_simple(self, refresh_token):
        """Simple token refresh"""
        try:
            url = f"{self.auth_url}/osn/auth/v1/refresh-token"
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            payload = {"refreshToken": refresh_token}
            
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                access_token = data.get("token")
                if access_token:
                    return access_token
            return None
        except Exception as e:
            print(f"❌ Error refreshing token: {str(e)}")
            return None

    def get_access_token(self):
        """Get current access token"""
        return self.saved_access_token

    def search_movie(self, movie_id):
        """Search for movie by ID"""
        try:
            if not self.saved_access_token:
                self.error_occurred.emit("No OSN+ access token available. Please login to OSN+ first.")
                return None

            url = f"{self.base_url}/osn/media/v1/get-watch-content"
            headers = {
                'accept': '*/*',
                'access_token': self.saved_access_token,
                'client_platform': 'web-osn',
                'content-type': 'application/json',
            }
            
            payload = {"contentIds": [movie_id]}
            
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get("watchContents") and movie_id in data["watchContents"]:
                    movie_data = data["watchContents"][movie_id]["movie"]["movie"]
                    
                    self.content_found.emit({
                        'type': 'movie',
                        'data': movie_data,
                        'movie_id': movie_id
                    })
                    
                    return movie_data
                else:
                    self.error_occurred.emit("Movie not found. This might be a series ID.")
                    return None
            else:
                self.error_occurred.emit(f"Failed to fetch movie. Status: {response.status_code}")
                return None
                
        except Exception as e:
            self.error_occurred.emit(f"Error searching movie: {str(e)}")
            return None

    def search_series(self, series_id):
        """Search for series by ID"""
        try:
            if not self.saved_access_token:
                self.error_occurred.emit("No OSN+ access token available. Please login to OSN+ first.")
                return None

            url = f"{self.auth_url}/osn/media/v1/get-series-page"
            headers = {
                'accept': '*/*',
                'access_token': self.saved_access_token,
                'client_platform': 'web-osn',
                'content-type': 'application/json',
            }
            
            payload = {"contentId": series_id}
            
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if not data.get('notFound', True):
                    self.content_found.emit({
                        'type': 'series',
                        'data': data,
                        'series_id': series_id
                    })
                    return data
                else:
                    self.error_occurred.emit("Series not found. Please check the ID.")
                    return None
            else:
                self.error_occurred.emit(f"Failed to fetch series. Status: {response.status_code}")
                return None
                
        except Exception as e:
            self.error_occurred.emit(f"Error searching series: {str(e)}")
            return None

    def search_content(self, content_id):
        """Search for content (try movie first, then series)"""
        print(f"🔍 Searching OSN+ content: {content_id}")
        
        # Try movie first
        movie_result = self.search_movie(content_id)
        if movie_result:
            return movie_result
            
        # If movie fails, try series
        series_result = self.search_series(content_id)
        if series_result:
            return series_result
            
        # If both fail
        self.error_occurred.emit("Content not found in OSN+. Please check the ID.")
        return None
