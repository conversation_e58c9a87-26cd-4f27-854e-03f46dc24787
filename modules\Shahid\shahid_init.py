# ///////////////////////////////////////////////////////////////
#
# SHAHID VIP INTEGRATION FOR YANGO
# Integration of Shahid VIP application into YANGO unified platform
#
# ///////////////////////////////////////////////////////////////

import sys
import os
from pathlib import Path
from PySide6.QtCore import *
from PySide6.QtGui import *
from PySide6.QtWidgets import *

# Add current directory to path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Import Shahid modules
try:
    from .shahid_api import ShahidAPI
    print("✅ Shahid<PERSON>I imported successfully")
except ImportError as e:
    print(f"❌ Error importing ShahidAPI: {e}")
    ShahidAPI = None

try:
    from .shahid_drm import ShahidDRM
    print("✅ ShahidDRM imported successfully")
except ImportError as e:
    print(f"❌ Error importing ShahidDRM: {e}")
    ShahidDRM = None

try:
    from .shahid_downloader import ShahidDownloader
    print("✅ ShahidDownloader imported successfully")
except ImportError as e:
    print(f"❌ Error importing ShahidDownloader: {e}")
    ShahidDownloader = None

try:
    from .shahid_ui import ShahidUI
    print("✅ ShahidUI imported successfully")
except ImportError as e:
    print(f"❌ Error importing ShahidUI: {e}")
    ShahidUI = None

try:
    from .browser_player import BrowserPlayerWidget
    print("✅ BrowserPlayerWidget imported successfully")
except ImportError as e:
    print(f"❌ Error importing BrowserPlayerWidget: {e}")
    BrowserPlayerWidget = None

try:
    from .settings_manager import SettingsManager
    print("✅ SettingsManager imported successfully")
except ImportError as e:
    print(f"❌ Error importing SettingsManager: {e}")
    SettingsManager = None

try:
    from .ui_main import Ui_MainWindow as ShahidUiMainWindow
    print("✅ ShahidUiMainWindow imported successfully")
except ImportError as e:
    print(f"❌ Error importing ShahidUiMainWindow: {e}")
    ShahidUiMainWindow = None

try:
    from .ui_functions import UIFunctions as ShahidUIFunctions
    print("✅ ShahidUIFunctions imported successfully")
except ImportError as e:
    print(f"❌ Error importing ShahidUIFunctions: {e}")
    ShahidUIFunctions = None

try:
    from .app_settings import Settings as ShahidSettings
    print("✅ ShahidSettings imported successfully")
except ImportError as e:
    print(f"❌ Error importing ShahidSettings: {e}")
    ShahidSettings = None

try:
    from .app_functions import AppFunctions as ShahidAppFunctions
    print("✅ ShahidAppFunctions imported successfully")
except ImportError as e:
    print(f"❌ Error importing ShahidAppFunctions: {e}")
    ShahidAppFunctions = None

class ShahidWidget(QMainWindow):
    """
    Shahid VIP Widget for integration into YANGO
    This widget contains the complete Shahid VIP application interface
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setObjectName("shahid_widget")

        # Initialize Shahid components
        self.init_shahid_components()

        # Setup UI
        self.setup_ui()

        # Connect signals
        self.connect_signals()

        # Check token and show/hide token input
        try:
            self.check_token()
        except Exception as e:
            print(f"❌ Error checking token: {e}")

        # Check token and show/hide token input
        try:
            self.check_token()
        except Exception as e:
            print(f"❌ Error checking token: {e}")

        # Load recent searches from config file
        try:
            self.load_recent_urls_from_file()
        except Exception as e:
            print(f"❌ Error loading recent searches: {e}")

        print("✅ Shahid VIP widget initialized successfully")

    def init_shahid_components(self):
        """Initialize Shahid VIP components"""
        try:
            # Load token
            self.token = self.load_token()
            
            # Initialize settings manager
            self.settings_manager = SettingsManager() if SettingsManager else None
            
            # Initialize Shahid modules
            if ShahidAPI:
                self.shahid_api = ShahidAPI(token=self.token)
            else:
                self.shahid_api = None
                
            if ShahidDRM and self.shahid_api:
                try:
                    # Initialize with correct parameter order: api, token
                    self.shahid_drm = ShahidDRM(api=self.shahid_api, token=self.token)
                    print("✅ Shahid DRM handler initialized")
                except Exception as e:
                    print(f"⚠️ Could not initialize Shahid DRM handler: {e}")
                    import traceback
                    traceback.print_exc()
                    self.shahid_drm = None
            else:
                print("⚠️ Shahid DRM handler not available")
                self.shahid_drm = None
                
            if ShahidDownloader and self.settings_manager:
                self.shahid_downloader = ShahidDownloader(settings_manager=self.settings_manager)
            else:
                self.shahid_downloader = None
                
            if BrowserPlayerWidget:
                self.shahid_player = BrowserPlayerWidget()
            else:
                self.shahid_player = None
            
            print("✅ Shahid components initialized")
            
        except Exception as e:
            print(f"❌ Error initializing Shahid components: {e}")
            self.create_error_ui(f"Failed to initialize Shahid components: {e}")
            return
    
    def setup_ui(self):
        """Setup the Shahid UI using original design"""
        try:
            if not ShahidUiMainWindow:
                print("⚠️ ShahidUiMainWindow not available, creating fallback")
                self.create_fallback_ui()
                return

            # Setup the original Shahid UI directly on this QMainWindow
            self.shahid_ui_main = ShahidUiMainWindow()
            self.shahid_ui_main.setupUi(self)

            # Set global widgets reference for Shahid
            global shahid_widgets
            shahid_widgets = self.shahid_ui_main

            # Initialize Shahid UI handler if available
            if ShahidUI:
                try:
                    self.shahid_ui = ShahidUI(self, self.shahid_ui_main)
                    # Set the main_window reference in shahid_ui to point to this widget
                    self.shahid_ui.main_window = self
                    print("✅ Shahid UI handler initialized")
                except Exception as e:
                    print(f"⚠️ Could not initialize Shahid UI handler: {e}")
                    self.shahid_ui = None
            else:
                self.shahid_ui = None

            # Apply original Shahid styling
            self.apply_shahid_styling()

            # Set default page
            if hasattr(self.shahid_ui_main, 'stackedWidget') and hasattr(self.shahid_ui_main, 'home'):
                self.shahid_ui_main.stackedWidget.setCurrentWidget(self.shahid_ui_main.home)
                print("✅ Set default page to home")

            # Hide the original Shahid sidebar since we're using YANGO's sidebar
            self.hide_original_sidebar()

            # Remove borders and frames
            self.remove_borders()

            print("✅ Original Shahid UI setup completed")

        except Exception as e:
            print(f"❌ Error setting up original Shahid UI: {e}")
            self.create_fallback_ui()


    
    def create_fallback_ui(self):
        """Create a fallback UI when Shahid modules are not available"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(30)
        
        # Title
        title = QLabel("⭐ SHAHID VIP")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            QLabel {
                font-size: 36px;
                font-weight: bold;
                color: #f1c40f;
                margin-bottom: 30px;
            }
        """)
        layout.addWidget(title)
        
        # Message
        message = QLabel("""
        🚧 Shahid VIP Integration 🚧
        
        Shahid VIP downloader is being integrated into YANGO.
        
        Features available:
        • Download Shahid VIP movies and series
        • Multiple quality options
        • Arabic subtitle support
        • Token-based authentication
        
        Please check that all Shahid VIP modules are properly installed.
        """)
        message.setAlignment(Qt.AlignCenter)
        message.setWordWrap(True)
        message.setStyleSheet("""
            QLabel {
                color: #f8f8f2;
                font-size: 16px;
                line-height: 1.8;
                padding: 30px;
                background-color: rgb(33, 37, 43);
                border-radius: 12px;
                border: 2px solid #f1c40f;
            }
        """)
        layout.addWidget(message)
        
        layout.addStretch()
    
    def create_error_ui(self, error_message):
        """Create error UI when initialization fails"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(40, 40, 40, 40)
        
        error_label = QLabel(f"❌ Shahid VIP Error\n\n{error_message}")
        error_label.setAlignment(Qt.AlignCenter)
        error_label.setWordWrap(True)
        error_label.setStyleSheet("""
            QLabel {
                color: #ff5555;
                font-size: 16px;
                padding: 30px;
                background-color: rgb(33, 37, 43);
                border-radius: 12px;
                border: 2px solid #ff5555;
            }
        """)
        layout.addWidget(error_label)
    
    def apply_shahid_styling(self):
        """Apply original Shahid styling"""
        try:
            # Apply the original Shahid theme if available
            if ShahidAppFunctions and hasattr(ShahidAppFunctions, 'setThemeHack'):
                ShahidAppFunctions.setThemeHack(self)
                print("✅ Applied original Shahid theme")

            # Apply basic Shahid styling
            self.setStyleSheet("""
                QWidget#shahid_main_widget {
                    background-color: rgb(27, 29, 35);
                }
                QWidget#shahid_widget {
                    background-color: rgb(27, 29, 35);
                }
            """)

            print("✅ Shahid styling applied")

        except Exception as e:
            print(f"❌ Error applying Shahid styling: {e}")
    
    def connect_signals(self):
        """Connect original Shahid signals"""
        try:
            # Connect sidebar buttons if available
            if hasattr(self.shahid_ui_main, 'btn_home'):
                self.shahid_ui_main.btn_home.clicked.connect(self.buttonClick)
                print("✅ Connected btn_home")

            if hasattr(self.shahid_ui_main, 'btn_widgets'):
                self.shahid_ui_main.btn_widgets.clicked.connect(self.buttonClick)
                print("✅ Connected btn_widgets")

            if hasattr(self.shahid_ui_main, 'btn_new'):
                self.shahid_ui_main.btn_new.clicked.connect(self.buttonClick)
                print("✅ Connected btn_new")

            if hasattr(self.shahid_ui_main, 'btn_save'):
                self.shahid_ui_main.btn_save.clicked.connect(self.buttonClick)
                print("✅ Connected btn_save")

            if self.shahid_ui:
                # Connect original Shahid UI signals if available
                if hasattr(self.shahid_ui, 'search_button'):
                    self.shahid_ui.search_button.clicked.connect(self.search_content)
                    print("✅ Connected Shahid search signals")

                if hasattr(self.shahid_ui, 'token_button'):
                    self.shahid_ui.token_button.clicked.connect(self.save_token_from_ui)
                    print("✅ Connected Shahid token signals")

                if hasattr(self.shahid_ui, 'clear_button'):
                    self.shahid_ui.clear_button.clicked.connect(self.clear_recent_urls)
                    print("✅ Connected Shahid clear signals")

                # Connect content tabs signal for tab changes
                if hasattr(self.shahid_ui, 'content_tabs'):
                    self.shahid_ui.content_tabs.currentChanged.connect(self.on_tab_changed)
                    print("✅ Connected Shahid content tabs signals")

                # Connect continue button for seasons tab
                if hasattr(self.shahid_ui, 'continue_button'):
                    self.shahid_ui.continue_button.clicked.connect(self.show_seasons_tab)
                    print("✅ Connected Shahid continue button")

                # Connect seasons list selection
                if hasattr(self.shahid_ui, 'seasons_list'):
                    self.shahid_ui.seasons_list.itemClicked.connect(self.load_episodes)
                    print("✅ Connected Shahid seasons list")

                # Connect episodes list selection to enable play buttons
                if hasattr(self.shahid_ui, 'episodes_list'):
                    self.shahid_ui.episodes_list.itemSelectionChanged.connect(self.on_episodes_selection_changed)
                    print("✅ Connected Shahid episodes list selection")

                # Connect seasons continue button
                if hasattr(self.shahid_ui, 'seasons_continue_button'):
                    self.shahid_ui.seasons_continue_button.clicked.connect(self.show_download_options_tab)
                    print("✅ Connected Shahid seasons continue button")

                # Connect download buttons
                if hasattr(self.shahid_ui, 'add_to_queue_button'):
                    self.shahid_ui.add_to_queue_button.clicked.connect(self.add_to_download_queue)
                    print("✅ Connected Shahid add to queue button")

                if hasattr(self.shahid_ui, 'download_with_idm_button'):
                    self.shahid_ui.download_with_idm_button.clicked.connect(self.download_with_idm)
                    print("✅ Connected Shahid download with IDM button")

                if hasattr(self.shahid_ui, 'start_download_button'):
                    self.shahid_ui.start_download_button.clicked.connect(self.start_downloads)
                    print("✅ Connected Shahid start download button")

                if hasattr(self.shahid_ui, 'clear_completed_button'):
                    self.shahid_ui.clear_completed_button.clicked.connect(self.clear_completed_downloads)
                    print("✅ Connected Shahid clear completed button")

                if hasattr(self.shahid_ui, 'clear_all_button'):
                    self.shahid_ui.clear_all_button.clicked.connect(self.clear_all_downloads)
                    print("✅ Connected Shahid clear all button")

                # Connect selection buttons
                if hasattr(self.shahid_ui, 'select_all_button'):
                    self.shahid_ui.select_all_button.clicked.connect(self.select_all_options)
                    print("✅ Connected Shahid select all button")

                if hasattr(self.shahid_ui, 'select_none_button'):
                    self.shahid_ui.select_none_button.clicked.connect(self.select_none_options)
                    print("✅ Connected Shahid select none button")

                # Connect recent URLs combo box selection
                if hasattr(self.shahid_ui, 'recent_combo'):
                    self.shahid_ui.recent_combo.currentTextChanged.connect(self.on_recent_selection_changed)
                    print("✅ Connected Shahid recent combo selection")

                # Connect play buttons
                if hasattr(self.shahid_ui, 'play_button'):
                    self.shahid_ui.play_button.clicked.connect(self.play_content)
                    print("✅ Connected Shahid main play button")

                if hasattr(self.shahid_ui, 'seasons_play_button'):
                    self.shahid_ui.seasons_play_button.clicked.connect(self.play_content)
                    print("✅ Connected Shahid seasons play button")

                if hasattr(self.shahid_ui, 'download_options_play_button'):
                    self.shahid_ui.download_options_play_button.clicked.connect(self.play_content)
                    print("✅ Connected Shahid download options play button")

                print("✅ Shahid signals connected")
            else:
                print("ℹ️ No Shahid UI available for signal connection")

        except Exception as e:
            print(f"❌ Error connecting Shahid signals: {e}")

    def buttonClick(self):
        """Handle button clicks in Shahid UI"""
        try:
            btn = self.sender()
            btnName = btn.objectName()

            print(f"🔘 Shahid button clicked: {btnName}")

            # Handle different button clicks
            if btnName == "btn_home":
                if hasattr(self.shahid_ui_main, 'stackedWidget') and hasattr(self.shahid_ui_main, 'home'):
                    self.shahid_ui_main.stackedWidget.setCurrentWidget(self.shahid_ui_main.home)
                    self.resetStyle(btnName)
                    btn.setStyleSheet(self.selectMenu(btn.styleSheet()))
                    print("🏠 Switched to Shahid home page")

            elif btnName == "btn_widgets":
                if hasattr(self.shahid_ui_main, 'stackedWidget') and hasattr(self.shahid_ui_main, 'widgets'):
                    self.shahid_ui_main.stackedWidget.setCurrentWidget(self.shahid_ui_main.widgets)
                    self.resetStyle(btnName)
                    btn.setStyleSheet(self.selectMenu(btn.styleSheet()))
                    print("🎬 Switched to Shahid movies page")

            elif btnName == "btn_new":
                if hasattr(self.shahid_ui_main, 'stackedWidget') and hasattr(self.shahid_ui_main, 'new_page'):
                    self.shahid_ui_main.stackedWidget.setCurrentWidget(self.shahid_ui_main.new_page)
                    self.resetStyle(btnName)
                    btn.setStyleSheet(self.selectMenu(btn.styleSheet()))
                    print("📺 Switched to Shahid series page")

            elif btnName == "btn_save":
                if hasattr(self.shahid_ui_main, 'stackedWidget') and hasattr(self.shahid_ui_main, 'home'):
                    self.shahid_ui_main.stackedWidget.setCurrentWidget(self.shahid_ui_main.home)
                    self.resetStyle(btnName)
                    btn.setStyleSheet(self.selectMenu(btn.styleSheet()))
                    print("⚙️ Switched to Shahid settings")

        except Exception as e:
            print(f"❌ Error in Shahid buttonClick: {e}")

    def selectMenu(self, getStyle):
        """Apply selected menu style"""
        try:
            if ShahidSettings and hasattr(ShahidSettings, 'MENU_SELECTED_STYLESHEET'):
                return getStyle + ShahidSettings.MENU_SELECTED_STYLESHEET
            else:
                # Fallback style
                return getStyle + """
                    border-left: 22px solid qlineargradient(spread:pad, x1:0.034, y1:0, x2:0.216, y2:0, stop:0.499 rgba(241, 196, 15, 255), stop:0.5 rgba(85, 170, 255, 0));
                    background-color: rgb(40, 44, 52);
                """
        except:
            return getStyle

    def resetStyle(self, widget):
        """Reset other button styles"""
        try:
            if hasattr(self.shahid_ui_main, 'topMenu'):
                for w in self.shahid_ui_main.topMenu.findChildren(QPushButton):
                    if w.objectName() != widget:
                        style = w.styleSheet()
                        if ShahidSettings and hasattr(ShahidSettings, 'MENU_SELECTED_STYLESHEET'):
                            w.setStyleSheet(style.replace(ShahidSettings.MENU_SELECTED_STYLESHEET, ""))
        except Exception as e:
            print(f"❌ Error resetting styles: {e}")

    def hide_original_sidebar(self):
        """Hide the original Shahid sidebar and extra UI components"""
        try:
            # Hide the left menu background (main sidebar)
            if hasattr(self.shahid_ui_main, 'leftMenuBg'):
                self.shahid_ui_main.leftMenuBg.setVisible(False)
                print("✅ Hidden original Shahid left menu")

            # Hide extra left box if exists
            if hasattr(self.shahid_ui_main, 'extraLeftBox'):
                self.shahid_ui_main.extraLeftBox.setVisible(False)
                print("✅ Hidden original Shahid extra left box")

            # Hide right settings box if exists
            if hasattr(self.shahid_ui_main, 'rightMenuBg'):
                self.shahid_ui_main.rightMenuBg.setVisible(False)
                print("✅ Hidden original Shahid right menu")

            # Hide any extra menus
            if hasattr(self.shahid_ui_main, 'extraTopMenu'):
                self.shahid_ui_main.extraTopMenu.setVisible(False)
                print("✅ Hidden original Shahid extra top menu")

            # Hide title bar and window controls
            if hasattr(self.shahid_ui_main, 'contentTopBg'):
                self.shahid_ui_main.contentTopBg.setVisible(False)
                print("✅ Hidden original Shahid title bar")

            # Hide right buttons (minimize, maximize, close)
            if hasattr(self.shahid_ui_main, 'rightButtons'):
                self.shahid_ui_main.rightButtons.setVisible(False)
                print("✅ Hidden original Shahid window controls")

            # Hide bottom bar with credits
            if hasattr(self.shahid_ui_main, 'bottomBar'):
                self.shahid_ui_main.bottomBar.setVisible(False)
                print("✅ Hidden original Shahid bottom bar")

            # Hide credits label specifically
            if hasattr(self.shahid_ui_main, 'creditsLabel'):
                self.shahid_ui_main.creditsLabel.setVisible(False)
                print("✅ Hidden original Shahid credits")

            # Hide version label
            if hasattr(self.shahid_ui_main, 'version'):
                self.shahid_ui_main.version.setVisible(False)
                print("✅ Hidden original Shahid version")

            # Hide size grip frame
            if hasattr(self.shahid_ui_main, 'frame_size_grip'):
                self.shahid_ui_main.frame_size_grip.setVisible(False)
                print("✅ Hidden original Shahid size grip")

            print("✅ Original Shahid sidebar and extra components hidden")

        except Exception as e:
            print(f"❌ Error hiding original sidebar: {e}")

    def remove_borders(self):
        """Remove all borders and frames to eliminate separators"""
        try:
            # Remove borders from main app container
            if hasattr(self.shahid_ui_main, 'bgApp'):
                self.shahid_ui_main.bgApp.setStyleSheet("""
                    QFrame#bgApp {
                        background-color: rgb(40, 44, 52);
                        border: none;
                    }
                """)
                print("✅ Removed bgApp borders")

            # Remove margins from app margins to eliminate outer frame
            if hasattr(self.shahid_ui_main, 'appMargins'):
                self.shahid_ui_main.appMargins.setContentsMargins(0, 0, 0, 0)
                print("✅ Removed app margins")

            # Remove borders from pages container
            if hasattr(self.shahid_ui_main, 'pagesContainer'):
                self.shahid_ui_main.pagesContainer.setStyleSheet("""
                    QFrame#pagesContainer {
                        border: none;
                    }
                """)
                # Remove margins from layout
                layout = self.shahid_ui_main.pagesContainer.layout()
                if layout:
                    layout.setContentsMargins(0, 0, 0, 0)
                    print("✅ Removed pages container borders")

            # Remove borders from content areas
            if hasattr(self.shahid_ui_main, 'contentBox'):
                self.shahid_ui_main.contentBox.setStyleSheet("""
                    QFrame#contentBox {
                        border: none;
                    }
                """)
                print("✅ Removed content box borders")

            # Remove any frame borders from main widget
            self.setStyleSheet("""
                QMainWindow {
                    border: none;
                }
                QFrame {
                    border: none;
                }
            """)

            print("✅ All borders and separators removed")

        except Exception as e:
            print(f"❌ Error removing borders: {e}")
    
    def load_token(self):
        """Load Shahid token from binaries folder"""
        try:
            # Get YANGO root directory
            yango_root = Path(__file__).parent.parent.parent

            # Try main binaries folder first
            token_path = yango_root / "binaries" / "shahid_token.txt"
            if token_path.exists():
                with open(token_path, 'r', encoding='utf-8') as f:
                    token = f.read().strip()
                print(f"✅ Shahid token loaded from: {token_path}")
                return token

            # Try login subfolder as backup
            login_token_path = yango_root / "binaries" / "login" / "shahid_token.txt"
            if login_token_path.exists():
                with open(login_token_path, 'r', encoding='utf-8') as f:
                    token = f.read().strip()
                print(f"✅ Shahid token loaded from: {login_token_path}")
                return token

            print(f"ℹ️ Shahid token file not found in binaries folder")
            return None

        except Exception as e:
            print(f"❌ Error loading Shahid token: {e}")
            return None
    
    def update_status(self, message):
        """Update status message"""
        print(f"📊 Shahid Status: {message}")
    
    def show_error(self, error_message):
        """Show error message"""
        print(f"❌ Shahid Error: {error_message}")
        QMessageBox.critical(self, "Shahid VIP Error", error_message)
    
    def show_content_found(self, content_data):
        """Show content found"""
        print(f"✅ Shahid Content found: {content_data}")

    def save_token_from_ui(self):
        """Save token from UI input"""
        try:
            if hasattr(self.shahid_ui, 'token_input'):
                token = self.shahid_ui.token_input.text().strip()
                if token:
                    # Save token to file
                    self.save_token(token)
                    QMessageBox.information(self, "Token Saved", "Your Shahid token has been saved successfully.")
                    print(f"✅ Shahid token saved successfully")

                    # Hide token frame if available
                    if hasattr(self.shahid_ui, 'token_frame'):
                        self.shahid_ui.token_frame.setVisible(False)
                else:
                    QMessageBox.warning(self, "Invalid Token", "Please enter a valid token.")
                    print("⚠️ Empty token provided")
            else:
                print("❌ Token input not found")

        except Exception as e:
            print(f"❌ Error saving token from UI: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save token: {str(e)}")

    def save_token(self, token):
        """Save token to file in main binaries folder"""
        try:
            # Get YANGO root directory (go up 3 levels from modules/Shahid/shahid_init.py)
            yango_root = Path(__file__).parent.parent.parent
            token_path = yango_root / "binaries" / "shahid_token.txt"

            # Create binaries directory if it doesn't exist
            token_path.parent.mkdir(parents=True, exist_ok=True)

            # Save token to file
            with open(token_path, 'w', encoding='utf-8') as f:
                f.write(token)

            print(f"✅ Shahid token saved to: {token_path}")

            # Also save in login subfolder for compatibility
            login_dir = yango_root / "binaries" / "login"
            login_dir.mkdir(parents=True, exist_ok=True)
            login_token_path = login_dir / "shahid_token.txt"

            with open(login_token_path, 'w', encoding='utf-8') as f:
                f.write(token)

            print(f"✅ Shahid token also saved to: {login_token_path}")
            return True

        except Exception as e:
            print(f"❌ Error saving Shahid token: {e}")
            return False

    def search_content(self):
        """Search for content using URL or ID."""
        try:
            if not hasattr(self.shahid_ui, 'url_input'):
                print("❌ URL input not found")
                return

            input_text = self.shahid_ui.url_input.text().strip()
            if not input_text:
                QMessageBox.warning(self, "Invalid Input", "Please enter a valid URL or content ID.")
                return

            # Check if token exists
            token = self.load_token()
            if not token:
                QMessageBox.warning(self, "Token Required", "Please enter your Shahid token first.")
                return

            # If input starts with "ID:", extract just the ID
            if input_text.startswith("ID: "):
                input_text = input_text.replace("ID: ", "")
                print(f"[UI] Extracted ID from input: {input_text}")

            # Show loading indicator
            self.shahid_ui.url_input.setDisabled(True)
            if hasattr(self.shahid_ui, 'search_button'):
                self.shahid_ui.search_button.setDisabled(True)
            QApplication.setOverrideCursor(Qt.WaitCursor)

            try:
                # Import and initialize Shahid API
                from .shahid_api import ShahidAPI
                api = ShahidAPI()
                api.token = token

                # Test URL extraction first
                content_id = api.extract_content_id_from_url(input_text)
                if content_id:
                    print(f"[UI] Successfully extracted content ID: {content_id} from input: {input_text}")
                else:
                    print(f"[UI] Failed to extract content ID from input: {input_text}")

                # Get content details
                print(f"[UI] Searching for content with input: {input_text}")
                content_details, content_type = api.get_content_details(input_text)

                if not content_details:
                    QMessageBox.warning(self, "Content Not Found", "Could not find content with the provided URL or ID.")
                    return

                # Print full API response for debugging
                import json
                print("API Response:")
                print(json.dumps(content_details, indent=2, ensure_ascii=False))

                # Get the title of the content
                title = None
                if content_details and 'productModel' in content_details:
                    title = content_details['productModel'].get('title', None)
                    print(f"[UI] Extracted title: {title}")

                # Add to recent URLs if not already there
                self.add_to_recent_urls(input_text, title)

                # Display content details
                self.display_content_details(content_details, content_type)

                # Show content tabs
                if hasattr(self.shahid_ui, 'content_tabs'):
                    self.shahid_ui.content_tabs.setVisible(True)
                    self.shahid_ui.content_tabs.setCurrentIndex(0)  # Show Content Info tab

                # Enable continue button and play button
                if hasattr(self.shahid_ui, 'continue_button'):
                    self.shahid_ui.continue_button.setEnabled(True)

                # Enable play button for movies (can play directly)
                if content_type == "MOVIE":
                    self.enable_play_buttons()
                    print("✅ Play buttons enabled for movie")

                # Store content info for later use
                self.current_content = {
                    'details': content_details,
                    'type': content_type,
                    'url': input_text
                }

            except ImportError as e:
                print(f"❌ Error importing Shahid API: {e}")
                QMessageBox.critical(self, "API Error", "Shahid API not available.")
            except Exception as e:
                print(f"❌ Error during search: {e}")
                QMessageBox.critical(self, "Search Error", f"Error during search: {str(e)}")
            finally:
                # Restore UI state
                if hasattr(self.shahid_ui, 'url_input'):
                    self.shahid_ui.url_input.setDisabled(False)
                if hasattr(self.shahid_ui, 'search_button'):
                    self.shahid_ui.search_button.setDisabled(False)
                QApplication.restoreOverrideCursor()

        except Exception as e:
            print(f"❌ Error searching content: {e}")
            QMessageBox.critical(self, "Search Error", f"Error during search: {str(e)}")

    def extract_content_id(self, url):
        """Extract content ID from Shahid URL"""
        try:
            import re

            # Pattern for Shahid URLs
            patterns = [
                r'series-(\d+)',  # For series
                r'movie-(\d+)',   # For movies
                r'/(\d+)$',       # For direct ID at end
                r'content[/-](\d+)',  # For content URLs
            ]

            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    return match.group(1)

            # If no pattern matches, check if it's already a number
            if url.isdigit():
                return url

            return None

        except Exception as e:
            print(f"❌ Error extracting content ID: {e}")
            return None

    def clear_recent_urls(self):
        """Clear recent URLs"""
        try:
            if hasattr(self.shahid_ui, 'recent_combo'):
                self.shahid_ui.recent_combo.clear()
                print("✅ Cleared recent URLs")

            if hasattr(self.shahid_ui, 'url_input'):
                self.shahid_ui.url_input.clear()
                print("✅ Cleared URL input")

        except Exception as e:
            print(f"❌ Error clearing recent URLs: {e}")

    def on_recent_selection_changed(self, selected_text):
        """Handle selection change in recent URLs combo box"""
        try:
            # Skip placeholder text and empty selections
            if (not selected_text.strip() or
                selected_text.startswith("Select from recent") or
                selected_text == ""):
                return

            print(f"🔄 Recent selection changed to: {selected_text}")

            # Extract ID from the selected text
            content_id = None
            if selected_text.startswith("ID: "):
                # Format: "ID: 12345 - Title" or "ID: 12345"
                parts = selected_text.split(" - ", 1)
                id_part = parts[0].replace("ID: ", "").strip()
                if id_part.isdigit():
                    content_id = id_part
            else:
                # Try to extract ID from URL or use as direct ID
                content_id = self.extract_content_id(selected_text)
                if not content_id and selected_text.isdigit():
                    content_id = selected_text

            if content_id:
                # Set the ID in the URL input field
                if hasattr(self.shahid_ui, 'url_input'):
                    self.shahid_ui.url_input.setText(content_id)
                    print(f"✅ Set URL input to: {content_id}")

                # Automatically trigger search
                print(f"🔍 Auto-triggering search for ID: {content_id}")
                self.search_content()
            else:
                print(f"⚠️ Could not extract valid ID from: {selected_text}")

        except Exception as e:
            print(f"❌ Error handling recent selection: {e}")

    def play_content(self):
        """Play content using player-shahid"""
        try:
            print("🎬 Play button clicked - Starting Shahid player...")

            # Check if we have current playout data (from Download Options tab)
            if hasattr(self, 'current_playout_data') and self.current_playout_data:
                print("✅ Using current playout data for playback")
                self.start_player_with_stream()
                return

            # Check if we have current content
            if not hasattr(self, 'current_content') or not self.current_content:
                QMessageBox.warning(self, "No Content", "Please search for content first and go to Download Options tab.")
                return

            # Check if player is available
            if not self.shahid_player:
                QMessageBox.warning(self, "Player Error", "Shahid player is not available.")
                return

            # Get content details
            content_details = self.current_content.get('details')
            content_type = self.current_content.get('type')

            if not content_details:
                QMessageBox.warning(self, "No Content Data", "No content data available for playback.")
                return

            print(f"🎬 Playing {content_type}: {content_details.get('productModel', {}).get('title', 'Unknown')}")

            # For movies, play directly
            if content_type == "MOVIE":
                self.play_movie(content_details)
            else:
                # For series, check if episodes are selected
                if hasattr(self.shahid_ui, 'episodes_list') and self.shahid_ui.episodes_list.selectedItems():
                    selected_episodes = self.shahid_ui.episodes_list.selectedItems()
                    if selected_episodes:
                        # Play first selected episode
                        episode_item = selected_episodes[0]
                        episode_data = episode_item.data(Qt.UserRole)
                        if episode_data:
                            self.play_episode(episode_data)
                        else:
                            QMessageBox.warning(self, "Episode Error", "No episode data available.")
                    else:
                        QMessageBox.warning(self, "No Episode Selected", "Please select an episode to play.")
                else:
                    QMessageBox.warning(self, "No Episodes", "Please load episodes first from the Seasons tab.")

        except Exception as e:
            print(f"❌ Error playing content: {e}")
            QMessageBox.critical(self, "Player Error", f"Error starting player: {str(e)}")

    def play_movie(self, content_details):
        """Play a movie"""
        try:
            print("🎬 Playing movie...")

            # Extract movie ID
            product = content_details.get('productModel', {})
            movie_id = product.get('id')

            if not movie_id:
                QMessageBox.warning(self, "Movie Error", "Could not find movie ID.")
                return

            # Get movie stream URL and keys
            stream_data = self.get_movie_stream_data(movie_id)
            if not stream_data:
                QMessageBox.warning(self, "Stream Error", "Could not get movie stream data.")
                return

            # Start player with stream data
            self.start_player_with_stream(stream_data)

        except Exception as e:
            print(f"❌ Error playing movie: {e}")
            QMessageBox.critical(self, "Movie Error", f"Error playing movie: {str(e)}")

    def play_episode(self, episode_data):
        """Play an episode"""
        try:
            print(f"🎬 Playing episode: {episode_data.get('title', 'Unknown')}")

            episode_id = episode_data.get('id')
            if not episode_id:
                QMessageBox.warning(self, "Episode Error", "Could not find episode ID.")
                return

            # Get episode stream URL and keys
            stream_data = self.get_episode_stream_data(episode_id)
            if not stream_data:
                QMessageBox.warning(self, "Stream Error", "Could not get episode stream data.")
                return

            # Start player with stream data
            self.start_player_with_stream(stream_data)

        except Exception as e:
            print(f"❌ Error playing episode: {e}")
            QMessageBox.critical(self, "Episode Error", f"Error playing episode: {str(e)}")

    def get_movie_stream_data(self, movie_id):
        """Get movie stream data"""
        try:
            # Use Shahid API to get stream data
            if not self.shahid_api:
                return None

            # Get movie playout data
            playout_data = self.shahid_api.get_movie_playout_url(movie_id)
            if not playout_data:
                return None

            # Extract stream data from playout
            stream_data = self.extract_stream_from_playout(playout_data)
            return stream_data

        except Exception as e:
            print(f"❌ Error getting movie stream data: {e}")
            return None

    def get_episode_stream_data(self, episode_id):
        """Get episode stream data"""
        try:
            # Use Shahid API to get stream data
            if not self.shahid_api:
                return None

            # Get episode playout data
            playout_data = self.shahid_api.get_episode_playout_url(episode_id)
            if not playout_data:
                return None

            # Extract stream data from playout
            stream_data = self.extract_stream_from_playout(playout_data)
            return stream_data

        except Exception as e:
            print(f"❌ Error getting episode stream data: {e}")
            return None

    def extract_stream_from_playout(self, playout_data):
        """Extract stream data from playout response"""
        try:
            if not playout_data:
                return None

            # Get MPD URLs from playout data
            mpd_urls = playout_data.get('mpd_urls', {})
            if not mpd_urls:
                print("❌ No MPD URLs found in playout data")
                return None

            # Prefer H264 over H265 for better compatibility
            stream_url = mpd_urls.get('H264') or mpd_urls.get('H265')
            if not stream_url:
                print("❌ No compatible stream URL found")
                return None

            # Extract DRM keys if available
            drm_keys = []
            if hasattr(self, 'current_playout_data') and self.current_playout_data:
                drm_info = self.current_playout_data.get('drm_info', {})
                if drm_info and 'formatted_key' in drm_info:
                    drm_keys = [drm_info['formatted_key']]

            # Create stream data object
            stream_data = {
                'url': stream_url,
                'keys': drm_keys,
                'title': 'Shahid Content',
                'codec': 'H264' if 'H264' in mpd_urls else 'H265'
            }

            print(f"✅ Extracted stream data: URL={stream_url[:50]}..., Keys={len(drm_keys)}")
            return stream_data

        except Exception as e:
            print(f"❌ Error extracting stream from playout: {e}")
            return None

    def select_best_stream(self, streams):
        """Select the best quality stream"""
        try:
            if not streams:
                return None

            # Sort streams by quality (highest first)
            sorted_streams = sorted(streams, key=lambda x: x.get('height', 0), reverse=True)
            return sorted_streams[0] if sorted_streams else None

        except Exception as e:
            print(f"❌ Error selecting best stream: {e}")
            return None

    def start_player_with_stream(self, stream_data=None):
        """Start player with stream data"""
        try:
            print("🚀 Starting Shahid player...")

            # Use current playout data if no stream_data provided
            if not stream_data and hasattr(self, 'current_playout_data') and self.current_playout_data:
                stream_url = self.current_playout_data.get('mpd_url')
                # Extract DRM key from drm_info
                drm_info = self.current_playout_data.get('drm_info', {})
                drm_key = drm_info.get('formatted_key') if drm_info else None
            else:
                # Extract stream URL and keys from provided data
                stream_url = stream_data.get('url') if stream_data else None
                drm_keys = stream_data.get('keys', []) if stream_data else []
                drm_key = drm_keys[0] if drm_keys else None

            if not stream_url:
                QMessageBox.warning(self, "Stream Error", "No stream URL available.")
                return

            print(f"🎬 Stream URL: {stream_url}")
            print(f"🔑 DRM Key: {drm_key if drm_key else 'None'}")

            # Start player using BrowserPlayerWidget (opens in separate browser window)
            success = self.shahid_player.load_player(
                mpd_url=stream_url,
                drm_key=drm_key if drm_key else None
            )

            if success:
                print("✅ Shahid player started successfully")
                # BrowserPlayerWidget will handle opening the browser window
                # No need to show the widget itself

            else:
                QMessageBox.warning(self, "Player Error", "Failed to start Shahid player.")

        except Exception as e:
            print(f"❌ Error starting player: {e}")
            QMessageBox.critical(self, "Player Error", f"Error starting player: {str(e)}")

    def enable_play_buttons(self):
        """Enable all play buttons"""
        try:
            if hasattr(self.shahid_ui, 'play_button'):
                self.shahid_ui.play_button.setEnabled(True)
                print("✅ Main play button enabled")

            if hasattr(self.shahid_ui, 'seasons_play_button'):
                self.shahid_ui.seasons_play_button.setEnabled(True)
                print("✅ Seasons play button enabled")

            if hasattr(self.shahid_ui, 'download_options_play_button'):
                self.shahid_ui.download_options_play_button.setEnabled(True)
                print("✅ Download options play button enabled")

        except Exception as e:
            print(f"❌ Error enabling play buttons: {e}")

    def disable_play_buttons(self):
        """Disable all play buttons"""
        try:
            if hasattr(self.shahid_ui, 'play_button'):
                self.shahid_ui.play_button.setEnabled(False)

            if hasattr(self.shahid_ui, 'seasons_play_button'):
                self.shahid_ui.seasons_play_button.setEnabled(False)

            if hasattr(self.shahid_ui, 'download_options_play_button'):
                self.shahid_ui.download_options_play_button.setEnabled(False)

            print("🔒 All play buttons disabled")

        except Exception as e:
            print(f"❌ Error disabling play buttons: {e}")

    def on_episodes_selection_changed(self):
        """Handle episodes selection change to enable/disable play buttons"""
        try:
            if hasattr(self.shahid_ui, 'episodes_list'):
                selected_episodes = self.shahid_ui.episodes_list.selectedItems()
                if selected_episodes:
                    # Enable play buttons when episodes are selected
                    self.enable_play_buttons()
                    print(f"✅ Play buttons enabled - {len(selected_episodes)} episodes selected")
                else:
                    # Disable play buttons when no episodes are selected (for series only)
                    if hasattr(self, 'current_content') and self.current_content.get('type') != "MOVIE":
                        self.disable_play_buttons()
                        print("🔒 Play buttons disabled - no episodes selected")

        except Exception as e:
            print(f"❌ Error handling episodes selection change: {e}")

    def add_to_recent_urls(self, url, title=None):
        """Add URL to recent URLs combobox with title if available."""
        try:
            if not hasattr(self.shahid_ui, 'recent_combo'):
                return

            # Extract the ID if it's a URL
            content_id = url if url.isdigit() else self.extract_content_id(url)

            # If no valid ID, simply add the URL as is
            if not content_id:
                # Check if it already exists
                if self.shahid_ui.recent_combo.findText(url) != -1:
                    self.shahid_ui.recent_combo.setCurrentText(url)
                    return

                # Add the complete URL
                self.shahid_ui.recent_combo.addItem(url)
                self.shahid_ui.recent_combo.setCurrentText(url)
                return

            # Create the display text with ID and title if available
            if title:
                id_display = f"ID: {content_id} - {title}"
            else:
                id_display = f"ID: {content_id}"

            # Search if this ID already exists (with or without title)
            for i in range(self.shahid_ui.recent_combo.count()):
                item_text = self.shahid_ui.recent_combo.itemText(i)
                # Check if the item contains this ID
                if f"ID: {content_id}" in item_text:
                    # If it exists but without title and now we have title, update it
                    if title and " - " not in item_text:
                        self.shahid_ui.recent_combo.removeItem(i)
                        self.shahid_ui.recent_combo.addItem(id_display)
                        self.shahid_ui.recent_combo.setCurrentText(id_display)
                    else:
                        # Already exists, just select it
                        self.shahid_ui.recent_combo.setCurrentText(item_text)
                    return

            # If we get here, the ID doesn't exist in the list, add it
            self.shahid_ui.recent_combo.addItem(id_display)
            self.shahid_ui.recent_combo.setCurrentText(id_display)

            # Save to config file immediately
            self.save_recent_urls_to_file()

        except Exception as e:
            print(f"❌ Error adding to recent URLs: {e}")

    def save_recent_urls_to_file(self):
        """Save recent URLs to config file for persistence"""
        try:
            import json
            import os

            # Create config directory if it doesn't exist
            config_dir = os.path.join(os.path.dirname(__file__), "config")
            os.makedirs(config_dir, exist_ok=True)

            # Get all items from recent combo, excluding placeholder text
            recent_items = []
            if hasattr(self.shahid_ui, 'recent_combo'):
                for i in range(self.shahid_ui.recent_combo.count()):
                    item_text = self.shahid_ui.recent_combo.itemText(i)
                    # Skip placeholder text and empty items
                    if (item_text.strip() and
                        not item_text.startswith("Select from recent") and
                        item_text not in recent_items):  # Avoid duplicates
                        recent_items.append(item_text.strip())

            # Save to JSON file
            config_file = os.path.join(config_dir, "recent_searches.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(recent_items, f, ensure_ascii=False, indent=2)

            print(f"💾 Saved {len(recent_items)} recent searches to {config_file}")

        except Exception as e:
            print(f"❌ Error saving recent searches to file: {str(e)}")

    def load_recent_urls_from_file(self):
        """Load recent URLs from config file"""
        try:
            import json
            import os

            config_file = os.path.join(os.path.dirname(__file__), "config", "recent_searches.json")

            if not os.path.exists(config_file):
                print(f"📄 No recent searches file found at {config_file}")
                return

            with open(config_file, 'r', encoding='utf-8') as f:
                recent_items = json.load(f)

            if hasattr(self.shahid_ui, 'recent_combo') and recent_items:
                # Clear existing items except placeholder
                self.shahid_ui.recent_combo.clear()

                # Add placeholder
                self.shahid_ui.recent_combo.addItem("Select from recent searches...")

                # Add loaded items
                for item in recent_items:
                    if item.strip():
                        self.shahid_ui.recent_combo.addItem(item.strip())

                print(f"📂 Loaded {len(recent_items)} recent searches from {config_file}")

        except Exception as e:
            print(f"❌ Error loading recent searches from file: {str(e)}")

    def display_content_details(self, content_details, content_type):
        """Display content details in the UI."""
        try:
            if not content_details or 'productModel' not in content_details:
                return

            product = content_details['productModel']

            # Set title with proper formatting
            title = product.get('title', 'Unknown Title')
            if hasattr(self.shahid_ui, 'title_label'):
                self.shahid_ui.title_label.setText(title)

            # Set type with proper formatting
            content_type_text = product.get('productType', content_type)
            if content_type_text == "SERIES":
                content_type_text = "SHOW"
            if hasattr(self.shahid_ui, 'type_label'):
                self.shahid_ui.type_label.setText(f"Type: {content_type_text}")

            # Extract year using the find_year_in_object function
            year = self.shahid_api.find_year_in_object(product)
            if hasattr(self.shahid_ui, 'year_label'):
                self.shahid_ui.year_label.setText(f"Year: {year}")

            # Set episodes count for series
            if content_type_text == "SHOW" or content_type == "SERIES":
                # Prioritize getting the total number of episodes from the season object
                episodes_count = 0

                # First, check directly in the season object (this is the most accurate source)
                if 'season' in product and isinstance(product['season'], dict):
                    season = product['season']
                    if 'numberOfEpisodes' in season:
                        episodes_count = season.get('numberOfEpisodes', 0)
                        print(f"Found numberOfEpisodes in season: {episodes_count}")

                # If not found in season, check in the main product object
                if not episodes_count:
                    if 'numberOfEpisodes' in product:
                        episodes_count = product.get('numberOfEpisodes', 0)
                        print(f"Found numberOfEpisodes in product: {episodes_count}")
                    elif 'totalNumberOfEpisodes' in product:
                        episodes_count = product.get('totalNumberOfEpisodes', 0)
                        print(f"Found totalNumberOfEpisodes in product: {episodes_count}")

                # If still not found, try to calculate from playlists
                if not episodes_count and 'season' in product and isinstance(product['season'], dict):
                    season = product['season']
                    if 'playlists' in season and isinstance(season['playlists'], list):
                        for playlist in season['playlists']:
                            if isinstance(playlist, dict) and playlist.get('productSubType') == 'EPISODE':
                                episodes_count = playlist.get('count', 0)
                                print(f"Found episodes count in playlist: {episodes_count}")
                                break

                if episodes_count and hasattr(self.shahid_ui, 'episodes_count_label'):
                    self.shahid_ui.episodes_count_label.setText(f"Episodes: {episodes_count}")
                    self.shahid_ui.episodes_count_label.setVisible(True)
                elif hasattr(self.shahid_ui, 'episodes_count_label'):
                    self.shahid_ui.episodes_count_label.setVisible(False)
            else:
                # Hide episodes count for movies
                if hasattr(self.shahid_ui, 'episodes_count_label'):
                    self.shahid_ui.episodes_count_label.setVisible(False)

            # Set genres with proper formatting
            genres = []
            if 'genres' in product and isinstance(product['genres'], list) and product['genres']:
                for genre in product['genres']:
                    if isinstance(genre, dict) and 'title' in genre and genre['title']:
                        genres.append(genre.get('title', ''))

            # Format genres with separator
            if genres:
                genres_text = ' | '.join(genres)
                if hasattr(self.shahid_ui, 'genres_label'):
                    self.shahid_ui.genres_label.setText(f"Genres: {genres_text}")
            else:
                # Try alternative genre fields
                alt_genres = []
                if 'genresList' in product and isinstance(product['genresList'], list):
                    for genre in product['genresList']:
                        if isinstance(genre, str) and genre:
                            alt_genres.append(genre)
                elif 'genresCombined' in product and product['genresCombined']:
                    # Try to get from genresCombined field (as seen in the JSON)
                    genres_combined = product['genresCombined'].split('-')
                    for genre in genres_combined:
                        if genre:
                            alt_genres.append(genre)

                if alt_genres:
                    alt_genres_text = ' | '.join(alt_genres)
                    if hasattr(self.shahid_ui, 'genres_label'):
                        self.shahid_ui.genres_label.setText(f"Genres: {alt_genres_text}")
                else:
                    if hasattr(self.shahid_ui, 'genres_label'):
                        self.shahid_ui.genres_label.setText("Genres: Unknown")

            # Set cast with proper formatting
            cast = []
            if 'cast' in product:
                for actor in product['cast']:
                    cast.append(actor.get('name', ''))
            elif 'persons' in product:
                for actor in product['persons']:
                    cast.append(actor.get('fullName', ''))

            # Format cast list
            if cast:
                cast_text = ', '.join(cast[:5])  # Limit to first 5 actors
                if hasattr(self.shahid_ui, 'cast_label'):
                    self.shahid_ui.cast_label.setText(f"Cast: {cast_text}")
            else:
                if hasattr(self.shahid_ui, 'cast_label'):
                    self.shahid_ui.cast_label.setText("Cast: Unknown")

            # Set description
            description = product.get('description', 'No description available.')
            if hasattr(self.shahid_ui, 'description_text'):
                self.shahid_ui.description_text.setText(description)

            print(f"✅ Displayed content details for: {title}")

            # Load poster image if available
            try:
                poster_url = self.shahid_api.extract_poster_url(content_details)
                if poster_url:
                    self.load_poster_image(poster_url)
                else:
                    if hasattr(self.shahid_ui, 'poster_label'):
                        self.shahid_ui.poster_label.setText("No Poster Available")
            except Exception as e:
                print(f"❌ Error loading poster: {e}")

        except Exception as e:
            print(f"❌ Error displaying content details: {e}")

    def check_token(self):
        """Check if token exists and show/hide token input accordingly."""
        try:
            print(f"🔍 Checking token...")
            print(f"🔍 Token value: {self.token}")
            print(f"🔍 Has shahid_ui: {hasattr(self, 'shahid_ui')}")
            if hasattr(self, 'shahid_ui') and self.shahid_ui:
                print(f"🔍 Has token_frame: {hasattr(self.shahid_ui, 'token_frame')}")

            if self.token:
                # Hide token input frame if token exists
                if hasattr(self.shahid_ui, 'token_frame'):
                    self.shahid_ui.token_frame.setVisible(False)
                    print(f"✅ Token found and loaded successfully. Token frame hidden.")
                else:
                    print(f"⚠️ Token found but token_frame not available")
            else:
                # Show token input frame if no token
                if hasattr(self.shahid_ui, 'token_frame'):
                    self.shahid_ui.token_frame.setVisible(True)
                    print(f"⚠️ No token found. Token frame shown.")
                else:
                    print(f"⚠️ No token found and token_frame not available")

        except Exception as e:
            print(f"❌ Error checking token: {e}")

    def load_poster_image(self, url):
        """Load poster image from URL and save it to the download directory."""
        try:
            print(f"Attempting to load poster from URL: {url}")

            # Set a placeholder loading message
            if hasattr(self.shahid_ui, 'poster_label'):
                self.shahid_ui.poster_label.setText("Loading poster...")

            # Use a session with headers to avoid potential blocking
            import requests
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
                'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9,ar;q=0.8',
                'Referer': 'https://shahid.mbc.net/'
            })

            # Try with proxies if available
            if hasattr(self, 'shahid_api') and hasattr(self.shahid_api, 'proxies') and self.shahid_api.proxies:
                response = session.get(url, proxies=self.shahid_api.proxies, timeout=10)
            else:
                response = session.get(url, timeout=10)

            print(f"Poster response status code: {response.status_code}")

            if response.status_code == 200:
                # Create a directory for cached images if it doesn't exist
                import os
                import hashlib
                from PySide6.QtGui import QPixmap

                # Get YANGO root directory (parent of modules directory)
                yango_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                cache_dir = os.path.join(yango_root, "cache", "posters")
                os.makedirs(cache_dir, exist_ok=True)

                # Create a filename based on the URL
                filename = hashlib.md5(url.encode()).hexdigest() + ".jpg"
                temp_file = os.path.join(cache_dir, filename)

                # Save the image to the cache file
                with open(temp_file, 'wb') as f:
                    f.write(response.content)

                # Load from file
                pixmap = QPixmap(temp_file)

                if not pixmap.isNull():
                    # We're using setScaledContents(True) on the label now, so no need to scale manually
                    if hasattr(self.shahid_ui, 'poster_label'):
                        self.shahid_ui.poster_label.setPixmap(pixmap)
                    print("Poster loaded successfully")
                else:
                    print("Failed to create pixmap from image data")
                    if hasattr(self.shahid_ui, 'poster_label'):
                        self.shahid_ui.poster_label.setText("Failed to load poster")
            else:
                print(f"Failed to load poster: HTTP {response.status_code}")
                if hasattr(self.shahid_ui, 'poster_label'):
                    self.shahid_ui.poster_label.setText("Failed to load poster")
        except Exception as e:
            print(f"Error loading poster: {e}")
            if hasattr(self.shahid_ui, 'poster_label'):
                self.shahid_ui.poster_label.setText("Error loading poster")

    # SEASONS AND EPISODES METHODS
    # ///////////////////////////////////////////////////////////////
    def show_seasons_tab(self):
        """Show seasons and episodes tab or skip to download options for movies."""
        # If content is a movie, skip directly to streams/qualities tab
        if hasattr(self, 'current_content') and self.current_content['type'] == "MOVIE":
            print("[UI] Content is a MOVIE, skipping seasons tab and going directly to streams/qualities")
            # For movies, we need to get the movie ID and load its streams directly
            content_details = self.current_content['details']
            if 'productModel' in content_details:
                product = content_details['productModel']
                movie_id = product.get('id')
                if movie_id:
                    print(f"[UI] Loading streams for movie ID: {movie_id}")
                    # Load movie stream options directly
                    self.load_movie_stream_options(movie_id)
                    # Skip to streams tab (index 2)
                    if hasattr(self.shahid_ui, 'content_tabs'):
                        self.shahid_ui.content_tabs.setCurrentIndex(2)  # Show Streams/Qualities tab
                else:
                    print("[UI ERROR] No movie ID found in content details")
            else:
                print("[UI ERROR] No productModel found in content details")
        else:
            # For series, show the seasons tab as usual
            if hasattr(self.shahid_ui, 'content_tabs'):
                self.shahid_ui.content_tabs.setCurrentIndex(1)  # Show Seasons & Episodes tab

        # The rest of the functionality is handled by on_tab_changed

    def on_tab_changed(self, index):
        """Handle tab change events."""
        print(f"Tab changed to index: {index}")

        # If changing to Seasons & Episodes tab
        if index == 1 and hasattr(self, 'current_content'):
            # Show loading indicator
            from PySide6.QtWidgets import QProgressDialog, QApplication
            from PySide6.QtCore import Qt
            progress_dialog = QProgressDialog("Loading seasons and episodes...", "Cancel", 0, 0, self)
            progress_dialog.setWindowTitle("Loading")
            progress_dialog.setWindowModality(Qt.WindowModal)
            progress_dialog.setMinimumDuration(0)
            progress_dialog.setValue(0)
            progress_dialog.show()
            QApplication.processEvents()

            try:
                print(f"[UI] Loading content type: {self.current_content['type']}")

                if self.current_content['type'] == "MOVIE":
                    print("[UI] Content is a MOVIE, should have been handled by show_seasons_tab")
                    # For movies, we should have already skipped to streams tab
                    # This shouldn't happen if show_seasons_tab worked correctly
                    content_details = self.current_content['details']
                    if 'productModel' in content_details:
                        product = content_details['productModel']
                        movie_id = product.get('id')
                        if movie_id:
                            print(f"[UI] Loading streams for movie ID: {movie_id} (fallback)")
                            # Load movie stream options directly
                            self.load_movie_stream_options(movie_id)
                            # Skip to streams tab (index 2)
                            if hasattr(self.shahid_ui, 'content_tabs'):
                                from PySide6.QtCore import QTimer
                                QTimer.singleShot(100, lambda: self.shahid_ui.content_tabs.setCurrentIndex(2))
                        else:
                            print("[UI ERROR] No movie ID found in content details")
                    else:
                        print("[UI ERROR] No productModel found in content details")
                else:
                    # For series, load seasons
                    content_details = self.current_content['details']
                    if 'productModel' in content_details:
                        product = content_details['productModel']
                        series_id = product.get('id')
                        if series_id:
                            print(f"[UI] Loading seasons for series ID: {series_id}")
                            self.load_seasons(series_id)
                        else:
                            print("[UI ERROR] No series ID found in content details")
                    else:
                        print("[UI ERROR] No productModel found in content details")

            except Exception as e:
                print(f"[UI ERROR] Error loading seasons and episodes: {e}")
                import traceback
                traceback.print_exc()
            finally:
                progress_dialog.close()

    def load_seasons(self, series_id):
        """Load seasons for a series."""
        print(f"[UI] Loading seasons for series ID: {series_id}")
        seasons = self.shahid_api.get_series_seasons(series_id)
        print(f"[UI] Found {len(seasons)} seasons")

        if hasattr(self.shahid_ui, 'seasons_list'):
            self.shahid_ui.seasons_list.clear()

            for season_data in seasons:
                # The API now returns more information including total episodes
                if len(season_data) >= 5:
                    season_id, season_number, season_name, episode_count, total_episodes = season_data
                    print(f"[UI] Season data (5+ fields): ID={season_id}, Number={season_number}, Name={season_name}, Count={episode_count}, Total={total_episodes}")
                else:
                    # Fallback for old API format
                    season_id, season_number, season_name, episode_count = season_data
                    total_episodes = episode_count
                    print(f"[UI] Season data (4 fields): ID={season_id}, Number={season_number}, Name={season_name}, Count={episode_count}")

                # If we found total episodes, display it in the season name
                display_name = season_name
                print(f"[UI] Adding season to list: {display_name}")

                from PySide6.QtWidgets import QListWidgetItem
                from PySide6.QtCore import Qt
                item = QListWidgetItem(display_name)
                item.setData(Qt.UserRole, {
                    'id': season_id,
                    'number': season_number,
                    'name': season_name,
                    'count': episode_count,
                    'total_episodes': total_episodes
                })
                self.shahid_ui.seasons_list.addItem(item)

            # Select first season if available
            if self.shahid_ui.seasons_list.count() > 0:
                print(f"[UI] Selecting first season")
                self.shahid_ui.seasons_list.setCurrentRow(0)
                # Load episodes for first season
                first_item = self.shahid_ui.seasons_list.item(0)
                self.load_episodes(first_item)
            else:
                print(f"[UI WARNING] No seasons found for series ID: {series_id}")

    def load_episodes(self, season_item):
        """Load episodes for a season."""
        from PySide6.QtCore import Qt
        season_data = season_item.data(Qt.UserRole)
        season_id = season_data['id']
        episode_count = season_data['count']
        total_episodes = season_data.get('total_episodes', episode_count)

        print(f"[UI] Loading episodes for season ID: {season_id}, Count: {episode_count}, Total: {total_episodes}")

        # Update range spinners if they exist
        if hasattr(self.shahid_ui, 'range_from'):
            self.shahid_ui.range_from.setMinimum(1)
            self.shahid_ui.range_from.setMaximum(total_episodes)
            self.shahid_ui.range_from.setValue(1)

        if hasattr(self.shahid_ui, 'range_to'):
            self.shahid_ui.range_to.setMinimum(1)
            self.shahid_ui.range_to.setMaximum(total_episodes)
            self.shahid_ui.range_to.setValue(total_episodes)
        print(f"[UI] Updated range spinners: from 1 to {total_episodes}")

        # If it's a movie (single episode), handle differently
        if self.current_content['type'] == "MOVIE":
            print("[UI] Content is a MOVIE, episodes already handled")
            # Make sure the continue button is enabled
            if hasattr(self.shahid_ui, 'seasons_continue_button'):
                self.shahid_ui.seasons_continue_button.setEnabled(True)
            # Already handled in show_seasons_tab
            return

        # For series, load episodes from API
        print(f"[UI] Fetching episodes from API for season ID: {season_id}")
        episodes = self.shahid_api.get_season_episodes(season_id)
        print(f"[UI] Found {len(episodes) if episodes else 0} episodes from API")

        if hasattr(self.shahid_ui, 'episodes_list'):
            self.shahid_ui.episodes_list.clear()

            # If we have episodes from the API
            if episodes:
                # Track episode numbers we've already added to avoid duplicates
                added_episode_numbers = set()
                available_count = 0

                for episode_data in episodes:
                    # The API now returns more information including availability
                    if len(episode_data) >= 6:
                        episode_id, episode_number, episode_name, episode_title, availability_status, is_available = episode_data
                        print(f"[UI] Episode data (6+ fields): ID={episode_id}, Number={episode_number}, Name={episode_name}, Title={episode_title}, Status={availability_status}, Available={is_available}")
                    else:
                        # Fallback for old API format
                        episode_id, episode_number, episode_name, episode_title = episode_data
                        availability_status = "Available"
                        is_available = True
                        print(f"[UI] Episode data (4 fields): ID={episode_id}, Number={episode_number}, Name={episode_name}, Title={episode_title}")

                    # Skip if we've already added this episode number
                    if episode_number in added_episode_numbers:
                        continue

                    # Skip episodes that are not available and don't have a specific release date
                    if not is_available and availability_status == "Not yet available":
                        continue

                    added_episode_numbers.add(episode_number)

                    # Count available episodes
                    if is_available:
                        available_count += 1

                    # Format display name based on availability
                    display_name = f"{episode_name} - {episode_title}"
                    if availability_status and availability_status != "Available":
                        display_name += f" - {availability_status}"

                    print(f"[UI] Adding episode to list: {display_name}")

                    from PySide6.QtWidgets import QListWidgetItem
                    from PySide6.QtCore import Qt
                    from PySide6.QtGui import QColor
                    item = QListWidgetItem(display_name)
                    item.setData(Qt.UserRole, {
                        'id': episode_id,
                        'number': episode_number,
                        'name': episode_name,
                        'title': episode_title,
                        'available': is_available,
                        'status': availability_status
                    })

                    # Set item color based on availability
                    if not is_available:
                        item.setForeground(QColor("#ff5555"))  # Red for unavailable

                    self.shahid_ui.episodes_list.addItem(item)

                print(f"[UI] Added {available_count} available episodes out of {len(added_episode_numbers)} total episodes shown")
            else:
                print(f"[UI] No episodes found from API")

            # Enable continue button if episodes are available
            episodes_count = self.shahid_ui.episodes_list.count()
            if hasattr(self.shahid_ui, 'seasons_continue_button'):
                self.shahid_ui.seasons_continue_button.setEnabled(episodes_count > 0)
            print(f"[UI] Added {episodes_count} episodes to list, continue button enabled: {episodes_count > 0}")

    def show_download_options_tab(self):
        """Show download options tab."""
        print(f"[DEBUG] show_download_options_tab called")
        print(f"[DEBUG] Has current_content: {hasattr(self, 'current_content')}")
        if hasattr(self, 'current_content'):
            print(f"[DEBUG] Current content type: {self.current_content.get('type', 'UNKNOWN')}")

        # For movies, skip episode selection check
        if hasattr(self, 'current_content') and self.current_content['type'] == "MOVIE":
            print("[UI] Content is a MOVIE, skipping episode selection check")
            # Show the download options tab directly
            if hasattr(self.shahid_ui, 'content_tabs'):
                self.shahid_ui.content_tabs.setCurrentIndex(2)  # Show Download Options tab
                print("[UI] Switched to Download Options tab for MOVIE")
            return

        # For series, check if any episodes are selected
        if hasattr(self.shahid_ui, 'episodes_list') and not self.shahid_ui.episodes_list.selectedItems():
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "No Episodes Selected", "Please select at least one episode to continue.")
            return

        # Get the first selected episode to load stream options
        if hasattr(self.shahid_ui, 'episodes_list'):
            selected_episodes = self.shahid_ui.episodes_list.selectedItems()
            if selected_episodes:
                selected_episode = selected_episodes[0]
                episode_data = selected_episode.data(Qt.UserRole)
                episode_id = episode_data['id']

                # Load stream options for the selected episode
                self.load_episode_stream_options(episode_id)
            else:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "No Episodes Selected", "Please select at least one episode to continue.")
                return

        # Show the download options tab
        if hasattr(self.shahid_ui, 'content_tabs'):
            self.shahid_ui.content_tabs.setCurrentIndex(2)  # Show Download Options tab

    def load_movie_stream_options(self, movie_id):
        """Load stream options for a movie."""
        print(f"[UI] Loading stream options for movie ID: {movie_id}")

        # Show loading indicator
        from PySide6.QtWidgets import QProgressDialog, QApplication, QMessageBox
        from PySide6.QtCore import Qt
        progress_dialog = QProgressDialog("Loading movie stream options...", "Cancel", 0, 0, self)
        progress_dialog.setWindowTitle("Loading")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.setValue(0)
        progress_dialog.show()
        QApplication.processEvents()

        try:
            # Get movie playout data from API
            playout_data = self.shahid_api.get_episode_playout_url(movie_id)
            if not playout_data:
                QMessageBox.warning(self, "Error", "Failed to get movie details.")
                progress_dialog.close()
                return

            # Print the full playout data for debugging
            print(f"[UI] Movie playout data: {playout_data}")

            # Extract available codecs
            available_codecs = playout_data.get('available_codecs', [])
            mpd_urls = playout_data.get('mpd_urls', {})
            audio_tracks = playout_data.get('audio_tracks', [])
            subtitle_tracks = playout_data.get('subtitle_tracks', [])

            print(f"[UI] Available codecs: {available_codecs}")
            print(f"[UI] MPD URLs: {mpd_urls}")
            print(f"[UI] Audio tracks: {audio_tracks}")
            print(f"[UI] Subtitle tracks: {subtitle_tracks}")

            # Store the playout data for later use
            self.current_playout_data = playout_data

            # Extract stream info for each available codec
            h264_qualities = []
            h265_qualities = []
            h264_audio_tracks = []
            h265_audio_tracks = []
            h264_subtitle_tracks = []
            h265_subtitle_tracks = []

            # Process H264
            if mpd_urls and isinstance(mpd_urls, dict) and 'H264' in mpd_urls:
                try:
                    mpd_url = mpd_urls['H264']
                    if not isinstance(mpd_url, str):
                        print(f"[ERROR] MPD URL is not a string: {type(mpd_url)}")
                    else:
                        # Fix URL if it contains "&" followed by another URL (common error in Shahid URLs)
                        if '&' in mpd_url and ('edgenextcdn.net' in mpd_url.split('&')[1] or 'mncdn.com' in mpd_url.split('&')[1]):
                            original_url = mpd_url
                            mpd_url = mpd_url.split('&')[0]
                            print(f"[UI] Fixed H264 MPD URL from: {original_url}")
                            print(f"[UI] Fixed H264 MPD URL to: {mpd_url}")
                            # Update the URL in mpd_urls
                            mpd_urls['H264'] = mpd_url

                        print(f"[UI] Extracting stream info from H264 MPD URL: {mpd_url}")

                        # Extract stream info from MPD
                        h264_qualities, h264_audio_tracks, h264_subtitle_tracks = self.shahid_downloader.extract_stream_info_from_url(mpd_url)

                        print(f"[UI] Extracted H264 qualities from MPD: {h264_qualities}")
                        print(f"[UI] Extracted H264 audio tracks from MPD: {h264_audio_tracks}")

                        # Save H264 information
                        self.current_playout_data['h264_qualities'] = h264_qualities
                        self.current_playout_data['h264_audio_tracks'] = h264_audio_tracks
                        self.current_playout_data['h264_subtitle_tracks'] = h264_subtitle_tracks

                        # Save MPD URL for playback
                        self.current_playout_data['mpd_url'] = mpd_url

                        # Save also in general variables for compatibility
                        self.current_playout_data['mpd_qualities'] = h264_qualities
                        self.current_playout_data['mpd_audio_tracks'] = h264_audio_tracks
                        self.current_playout_data['mpd_subtitle_tracks'] = h264_subtitle_tracks

                        # Process DRM info with H264 MPD
                        self.process_drm_info(movie_id, mpd_url)
                except Exception as e:
                    print(f"[ERROR] Error extracting H264 stream info from MPD: {e}")
                    import traceback
                    traceback.print_exc()

            # Process H265 if available
            if mpd_urls and isinstance(mpd_urls, dict) and 'H265' in mpd_urls:
                try:
                    mpd_url = mpd_urls['H265']
                    if not isinstance(mpd_url, str):
                        print(f"[ERROR] H265 MPD URL is not a string: {type(mpd_url)}")
                    else:
                        # Fix URL if it contains "&" followed by another URL (common error in Shahid URLs)
                        if '&' in mpd_url and ('edgenextcdn.net' in mpd_url.split('&')[1] or 'mncdn.com' in mpd_url.split('&')[1]):
                            original_url = mpd_url
                            mpd_url = mpd_url.split('&')[0]
                            print(f"[UI] Fixed H265 MPD URL from: {original_url}")
                            print(f"[UI] Fixed H265 MPD URL to: {mpd_url}")
                            # Update the URL in mpd_urls
                            mpd_urls['H265'] = mpd_url

                        print(f"[UI] Extracting stream info from H265 MPD URL: {mpd_url}")

                        # Extract stream info from MPD
                        h265_qualities, h265_audio_tracks, h265_subtitle_tracks = self.shahid_downloader.extract_stream_info_from_url(mpd_url)

                        print(f"[UI] Extracted H265 qualities from MPD: {h265_qualities}")
                        print(f"[UI] Extracted H265 audio tracks from MPD: {h265_audio_tracks}")

                        # Save H265 information
                        self.current_playout_data['h265_qualities'] = h265_qualities
                        self.current_playout_data['h265_audio_tracks'] = h265_audio_tracks
                        self.current_playout_data['h265_subtitle_tracks'] = h265_subtitle_tracks
                except Exception as e:
                    print(f"[ERROR] Error extracting H265 stream info from MPD: {e}")
                    import traceback
                    traceback.print_exc()

            # Update the user interface with available options
            self.update_download_options_ui()

        except Exception as e:
            print(f"[ERROR] Failed to load movie stream options: {e}")
            QMessageBox.warning(self, "Error", f"Failed to load movie stream options: {str(e)}")
        finally:
            progress_dialog.close()

    def load_episode_stream_options(self, episode_id):
        """Load stream options for the selected episode."""
        print(f"[UI] Loading stream options for episode ID: {episode_id}")

        # Show loading indicator
        from PySide6.QtWidgets import QProgressDialog, QApplication, QMessageBox
        from PySide6.QtCore import Qt
        progress_dialog = QProgressDialog("Loading stream options...", "Cancel", 0, 0, self)
        progress_dialog.setWindowTitle("Loading")
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)
        progress_dialog.setValue(0)
        progress_dialog.show()
        QApplication.processEvents()

        try:
            # Get episode details from API
            playout_data = self.shahid_api.get_episode_playout_url(episode_id)
            if not playout_data:
                QMessageBox.warning(self, "Error", "Failed to get episode details.")
                progress_dialog.close()
                return

            # Print the full playout data for debugging
            print(f"[UI] Playout data: {playout_data}")

            # Extract available codecs
            available_codecs = playout_data.get('available_codecs', [])
            mpd_urls = playout_data.get('mpd_urls', {})
            audio_tracks = playout_data.get('audio_tracks', [])
            subtitle_tracks = playout_data.get('subtitle_tracks', [])

            print(f"[UI] Available codecs: {available_codecs}")
            print(f"[UI] MPD URLs: {mpd_urls}")
            print(f"[UI] Audio tracks: {audio_tracks}")
            print(f"[UI] Subtitle tracks: {subtitle_tracks}")

            # Store the playout data for later use
            self.current_playout_data = playout_data

            # Extract stream info for each available codec
            h264_qualities = []
            h265_qualities = []
            h264_audio_tracks = []
            h265_audio_tracks = []
            h264_subtitle_tracks = []
            h265_subtitle_tracks = []

            # Process H264
            if mpd_urls and isinstance(mpd_urls, dict) and 'H264' in mpd_urls:
                try:
                    mpd_url = mpd_urls['H264']
                    if not isinstance(mpd_url, str):
                        print(f"[ERROR] MPD URL is not a string: {type(mpd_url)}")
                    else:
                        # Fix URL if it contains "&" followed by another URL (common error in Shahid URLs)
                        if '&' in mpd_url and ('edgenextcdn.net' in mpd_url.split('&')[1] or 'mncdn.com' in mpd_url.split('&')[1]):
                            original_url = mpd_url
                            mpd_url = mpd_url.split('&')[0]
                            print(f"[UI] Fixed H264 MPD URL from: {original_url}")
                            print(f"[UI] Fixed H264 MPD URL to: {mpd_url}")
                            # Update the URL in mpd_urls
                            mpd_urls['H264'] = mpd_url

                        print(f"[UI] Extracting stream info from H264 MPD URL: {mpd_url}")

                        # Extract stream info from MPD
                        h264_qualities, h264_audio_tracks, h264_subtitle_tracks = self.shahid_downloader.extract_stream_info_from_url(mpd_url)

                        print(f"[UI] Extracted H264 qualities from MPD: {h264_qualities}")
                        print(f"[UI] Extracted H264 audio tracks from MPD: {h264_audio_tracks}")

                        # Save H264 information
                        self.current_playout_data['h264_qualities'] = h264_qualities
                        self.current_playout_data['h264_audio_tracks'] = h264_audio_tracks
                        self.current_playout_data['h264_subtitle_tracks'] = h264_subtitle_tracks

                        # Save MPD URL for playback
                        self.current_playout_data['mpd_url'] = mpd_url

                        # Save also in general variables for compatibility
                        self.current_playout_data['mpd_qualities'] = h264_qualities
                        self.current_playout_data['mpd_audio_tracks'] = h264_audio_tracks
                        self.current_playout_data['mpd_subtitle_tracks'] = h264_subtitle_tracks

                        # Process DRM info with H264 MPD
                        self.process_drm_info(episode_id, mpd_url)
                except Exception as e:
                    print(f"[ERROR] Error extracting H264 stream info from MPD: {e}")
                    import traceback
                    traceback.print_exc()

            # Process H265
            if mpd_urls and isinstance(mpd_urls, dict) and 'H265' in mpd_urls:
                try:
                    mpd_url = mpd_urls['H265']
                    if not isinstance(mpd_url, str):
                        print(f"[ERROR] H265 MPD URL is not a string: {type(mpd_url)}")
                    else:
                        # Fix URL if it contains "&" followed by another URL (common error in Shahid URLs)
                        if '&' in mpd_url and ('edgenextcdn.net' in mpd_url.split('&')[1] or 'mncdn.com' in mpd_url.split('&')[1]):
                            original_url = mpd_url
                            mpd_url = mpd_url.split('&')[0]
                            print(f"[UI] Fixed H265 MPD URL from: {original_url}")
                            print(f"[UI] Fixed H265 MPD URL to: {mpd_url}")
                            # Update the URL in mpd_urls
                            mpd_urls['H265'] = mpd_url

                        print(f"[UI] Extracting stream info from H265 MPD URL: {mpd_url}")

                        # Extract stream info from MPD
                        h265_qualities, h265_audio_tracks, h265_subtitle_tracks = self.shahid_downloader.extract_stream_info_from_url(mpd_url)

                        print(f"[UI] Extracted H265 qualities from MPD: {h265_qualities}")
                        print(f"[UI] Extracted H265 audio tracks from MPD: {h265_audio_tracks}")

                        # Save H265 information
                        self.current_playout_data['h265_qualities'] = h265_qualities
                        self.current_playout_data['h265_audio_tracks'] = h265_audio_tracks
                        self.current_playout_data['h265_subtitle_tracks'] = h265_subtitle_tracks
                except Exception as e:
                    print(f"[ERROR] Error extracting H265 stream info from MPD: {e}")
                    import traceback
                    traceback.print_exc()

            # Update the user interface with available options
            self.update_download_options_ui()

        except Exception as e:
            print(f"[ERROR] Failed to load stream options: {e}")
            QMessageBox.warning(self, "Error", f"Failed to load stream options: {str(e)}")
        finally:
            progress_dialog.close()

    def process_drm_info(self, episode_id, mpd_url):
        """Process DRM information for an episode."""
        try:
            if hasattr(self, 'shahid_drm') and self.shahid_drm:
                drm_info = self.shahid_drm.process_episode_drm(episode_id, mpd_url)
                if drm_info:
                    print(f"[UI] Successfully extracted DRM info for episode {episode_id}")
                    print(f"[UI] DRM KID: {drm_info.get('kid', 'N/A')}")
                    print(f"[UI] DRM KEY: {drm_info.get('formatted_key', 'N/A')}")
                    # Store DRM info in current playout data
                    self.current_playout_data['drm_info'] = drm_info
                else:
                    print(f"[UI] No DRM info found for episode {episode_id}")
            else:
                print(f"[UI] DRM module not available")
        except Exception as e:
            print(f"[UI] Error processing DRM info: {e}")

    def extract_drm_from_mpd(self, mpd_url):
        """Extract PSSH and KID directly from MPD content."""
        try:
            import requests
            import re

            print(f"[DRM] Extracting DRM info from MPD: {mpd_url}")

            # Request MPD content
            headers = {
                'authority': 'api2.shahid.net',
                'accept': 'application/json',
                'accept-language': 'en',
                'content-type': 'application/json',
                'language': 'en',
                'origin': 'https://shahid.mbc.net',
                'referer': 'https://shahid.mbc.net/',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.0.0 Safari/537.36',
                'uuid': 'web',
            }

            response = requests.get(mpd_url, headers=headers)

            if response.status_code == 200:
                data = response.content.decode('utf-8')

                # Extract PSSH
                pssh = None
                match_obj = re.findall(r'pssh.+<', data, re.M | re.I)
                if match_obj and '>' in match_obj[0] and '<' in match_obj[0].split('>')[1]:
                    pssh = match_obj[0].split('>')[1].split('<')[0]
                    print(f"[DRM] Extracted PSSH: {pssh}")

                # Extract KID
                kid = None
                kid_match = re.search(r'cenc:default_KID="([0-9a-fA-F-]+)"', data)
                if kid_match:
                    kid = kid_match.group(1).replace('-', '').lower()
                    print(f"[DRM] Extracted KID: {kid}")

                if pssh and kid:
                    return {
                        'pssh': pssh,
                        'kid': kid,
                        'key': None,  # We don't have the key yet
                        'formatted_key': f"{kid}:KEY_NEEDED"
                    }
                else:
                    print(f"[DRM] Failed to extract PSSH or KID from MPD")
                    return None
            else:
                print(f"[DRM] Failed to fetch MPD. Status code: {response.status_code}")
                return None

        except Exception as e:
            print(f"[DRM] Error extracting DRM from MPD: {e}")
            import traceback
            traceback.print_exc()
            return None

    def update_download_options_ui(self):
        """Update the download options UI with available options from the MPD."""
        if not hasattr(self, 'current_playout_data'):
            return

        # Determine which codec is currently selected
        selected_codec = "H264"  # Default H264
        if hasattr(self.shahid_ui, 'h265_checkbox') and self.shahid_ui.h265_checkbox.isChecked():
            selected_codec = "H265"
        elif hasattr(self.shahid_ui, 'h264_checkbox') and self.shahid_ui.h264_checkbox.isChecked():
            selected_codec = "H264"

        # Update available resolutions for the selected codec
        self.update_resolutions_for_codec(selected_codec)

        # Update available audio tracks, filtering unknown languages
        self.update_audio_tracks_for_codec(selected_codec)

        # Update subtitle options
        subtitle_tracks = self.current_playout_data.get('mpd_subtitle_tracks', [])

        # Clear subtitle list
        if hasattr(self.shahid_ui, 'subtitle_list_widget'):
            self.shahid_ui.subtitle_list_widget.clear()

        # Update hidden checkboxes (for compatibility)
        if hasattr(self.shahid_ui, 'subtitle_ar_checkbox'):
            self.shahid_ui.subtitle_ar_checkbox.setChecked(False)
        if hasattr(self.shahid_ui, 'subtitle_en_checkbox'):
            self.shahid_ui.subtitle_en_checkbox.setChecked(False)

        if not subtitle_tracks:
            print("[UI] No subtitle tracks available")
        else:
            print(f"[UI] Available subtitle tracks: {subtitle_tracks}")

            # Add subtitles to the list
            for track in subtitle_tracks:
                code = track.get('code', '')
                name = track.get('name', '')

                # Verify that the code is valid (2-3 characters)
                if not code or len(code) not in [2, 3]:
                    print(f"[UI] Skipping invalid subtitle code: {code}")
                    continue

                # Make sure the name is not empty
                if not name:
                    try:
                        # Try to get the language name from the code
                        from langcodes import Language
                        name = Language.get(code).display_name()
                    except Exception:
                        # If we can't get the name, use the code as name
                        name = code.upper()

                display = f"{name} ({code})"
                print(f"[UI] Adding subtitle to list: {display}")

                # Add to list with default selection for ar and en
                selected = code in ['ar', 'en']
                if hasattr(self.shahid_ui, 'add_subtitle_to_list'):
                    self.shahid_ui.add_subtitle_to_list(display, selected)

                # Update hidden checkboxes for compatibility
                if code == 'ar' and hasattr(self.shahid_ui, 'subtitle_ar_checkbox'):
                    self.shahid_ui.subtitle_ar_checkbox.setText(display)
                    self.shahid_ui.subtitle_ar_checkbox.setChecked(selected)
                elif code == 'en' and hasattr(self.shahid_ui, 'subtitle_en_checkbox'):
                    self.shahid_ui.subtitle_en_checkbox.setText(display)
                    self.shahid_ui.subtitle_en_checkbox.setChecked(selected)

    def update_resolutions_for_codec(self, codec):
        """Update available resolutions based on selected codec."""
        if not hasattr(self, 'current_playout_data'):
            return

        # Clear resolution combo
        if hasattr(self.shahid_ui, 'resolution_combo'):
            self.shahid_ui.resolution_combo.clear()

        # Get available qualities for the selected codec
        qualities = []

        # Use codec-specific qualities if available
        if codec == "H264" and 'h264_qualities' in self.current_playout_data:
            codec_qualities = self.current_playout_data.get('h264_qualities', [])
            print(f"[UI] Using H264-specific qualities: {codec_qualities}")

            # Extract resolutions
            for quality in codec_qualities:
                if 'x' in quality[0]:
                    # Use _ to ignore the width variable that is not used
                    _, height = quality[0].split('x')
                    qualities.append(f"{height}p")
        elif codec == "H265" and 'h265_qualities' in self.current_playout_data:
            codec_qualities = self.current_playout_data.get('h265_qualities', [])
            print(f"[UI] Using H265-specific qualities: {codec_qualities}")

            # Extract resolutions
            for quality in codec_qualities:
                if 'x' in quality[0]:
                    # Use _ to ignore the width variable that is not used
                    _, height = quality[0].split('x')
                    qualities.append(f"{height}p")
        else:
            # Try with general MPD qualities
            mpd_qualities = self.current_playout_data.get('mpd_qualities', [])
            if mpd_qualities:
                # Filter qualities by codec if possible
                for quality in mpd_qualities:
                    if len(quality) > 1 and codec.lower() in quality[1].lower():
                        if 'x' in quality[0]:
                            # Use _ to ignore the width variable that is not used
                            _, height = quality[0].split('x')
                            qualities.append(f"{height}p")

        # If no codec-specific qualities, try with available codecs
        if not qualities:
            available_codecs = self.current_playout_data.get('available_codecs', [])
            for codec_info in available_codecs:
                if codec_info.get('name', '').upper() == codec:
                    resolution = codec_info.get('resolution', '')
                    if 'x' in resolution:
                        # Use _ to ignore the width variable that is not used
                        _, height = resolution.split('x')
                        qualities.append(f"{height}p")
                    elif resolution == 'HD':
                        qualities.append("720p")
                    elif resolution == '4K':
                        qualities.append("2160p")

        # If no qualities available, leave combobox empty
        if not qualities:
            print(f"[UI] No qualities available for codec {codec}")
            # Disable combobox to indicate no options available
            if hasattr(self.shahid_ui, 'resolution_combo'):
                self.shahid_ui.resolution_combo.setEnabled(False)
                # Show message in combobox
                self.shahid_ui.resolution_combo.addItem("No qualities available")
            return

        # Remove duplicates and sort
        unique_qualities = sorted(set(qualities), key=lambda x: int(x[:-1]), reverse=True)

        # Enable combobox since qualities are available
        if hasattr(self.shahid_ui, 'resolution_combo'):
            self.shahid_ui.resolution_combo.setEnabled(True)

            # Add to combo
            self.shahid_ui.resolution_combo.addItems(unique_qualities)

            # Select 360p by default if available
            if "360p" in unique_qualities:
                self.shahid_ui.resolution_combo.setCurrentText("360p")
            else:
                # If 360p not available, select lowest resolution
                self.shahid_ui.resolution_combo.setCurrentIndex(len(unique_qualities) - 1)

        print(f"[UI] Updated resolutions for codec {codec}: {unique_qualities}")

    def update_audio_tracks_for_codec(self, codec):
        """Update available audio tracks based on selected codec."""
        if not hasattr(self, 'current_playout_data'):
            return

        # Get available audio tracks according to codec
        audio_tracks = []
        if codec == "H264" and 'h264_audio_tracks' in self.current_playout_data:
            audio_tracks = self.current_playout_data.get('h264_audio_tracks', [])
            print(f"[UI] Using H264-specific audio tracks: {audio_tracks}")
        elif codec == "H265" and 'h265_audio_tracks' in self.current_playout_data:
            audio_tracks = self.current_playout_data.get('h265_audio_tracks', [])
            print(f"[UI] Using H265-specific audio tracks: {audio_tracks}")
        else:
            # Use general audio tracks
            audio_tracks = self.current_playout_data.get('mpd_audio_tracks', [])
            print(f"[UI] Using general audio tracks: {audio_tracks}")

        if not audio_tracks:
            print(f"[UI] No audio tracks found for codec {codec}")

            # Clear all existing audio checkboxes
            if hasattr(self.shahid_ui, 'audio_group_layout'):
                while self.shahid_ui.audio_group_layout.count() > 0:
                    item = self.shahid_ui.audio_group_layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()

                # Add message indicating no audio tracks available
                from PySide6.QtWidgets import QLabel
                label = QLabel("No audio tracks available")
                label.setStyleSheet("color: #f8f8f2;")
                self.shahid_ui.audio_group_layout.addWidget(label)
            return

        # Clear all existing audio checkboxes
        if hasattr(self.shahid_ui, 'audio_group_layout'):
            while self.shahid_ui.audio_group_layout.count() > 0:
                item = self.shahid_ui.audio_group_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()

        # Filter invalid audio tracks (like 'qad')
        valid_audio_tracks = []
        for track in audio_tracks:
            code = track.get('code', '')
            # Filter unknown or invalid language codes
            # Only allow standard language codes (2 or 3 characters)
            if code not in ['qad', 'qor', 'qae', 'qaf', 'qag', 'qaa', 'qab', 'qac'] and len(code) in [2, 3]:
                # Verify it's a real language
                try:
                    # Try to get language name to verify it's valid
                    from langcodes import Language
                    display_name = Language.get(code).display_name()
                    if display_name:
                        valid_audio_tracks.append(track)
                        print(f"[UI] Added valid audio track: {code} ({display_name})")
                except Exception as e:
                    print(f"[UI] Skipping invalid audio track: {code}, error: {e}")
                    continue

        # If no valid tracks, show message
        if not valid_audio_tracks:
            from PySide6.QtWidgets import QLabel
            label = QLabel("No audio tracks available")
            label.setStyleSheet("color: #f8f8f2;")
            if hasattr(self.shahid_ui, 'audio_group_layout'):
                self.shahid_ui.audio_group_layout.addWidget(label)
            return

        # Create new checkboxes for each valid audio track
        for track in valid_audio_tracks:
            code = track.get('code', '')
            name = track.get('name', '')

            # Capitalize language name
            if name.lower() == 'arabic':
                display = f"Arabic (ar)"
            elif name.lower() == 'english':
                display = f"English (en)"
            elif name.lower() == 'turkish':
                display = f"Turkish (tr)"
            else:
                display = f"{name.capitalize()} ({code})"

            # Create checkbox with style
            from PySide6.QtWidgets import QCheckBox
            checkbox = QCheckBox(display)
            checkbox.setStyleSheet("""
                QCheckBox {
                    color: #f8f8f2;
                    spacing: 5px;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                }
                QCheckBox::indicator:unchecked {
                    border: 1px solid #bd93f9;
                    background-color: #282a36;
                    border-radius: 9px;
                }
                QCheckBox::indicator:checked {
                    border: 1px solid #bd93f9;
                    background-color: #bd93f9;
                    border-radius: 9px;
                }
            """)
            checkbox.setChecked(True)  # Check by default

            # Save reference to standard checkboxes for compatibility
            if code == 'ar':
                self.shahid_ui.audio_ar_checkbox = checkbox
            elif code == 'en':
                self.shahid_ui.audio_en_checkbox = checkbox
            elif code == 'tr':
                self.shahid_ui.audio_tr_checkbox = checkbox

            if hasattr(self.shahid_ui, 'audio_group_layout'):
                self.shahid_ui.audio_group_layout.addWidget(checkbox)

        print(f"[UI] Updated audio tracks for codec {codec}: {[track.get('code', '') for track in valid_audio_tracks]}")

    def add_to_download_queue(self):
        """Add selected episodes to download queue."""
        from PySide6.QtWidgets import QMessageBox, QTableWidgetItem, QProgressBar, QPushButton, QWidget, QHBoxLayout
        from PySide6.QtCore import Qt
        import time

        # Check if any codec is selected
        if not (self.shahid_ui.h264_checkbox.isChecked() or self.shahid_ui.h265_checkbox.isChecked()):
            QMessageBox.warning(self, "No Codec Selected", "Please select at least one codec.")
            return

        # For movies, handle differently
        if hasattr(self, 'current_content') and self.current_content['type'] == "MOVIE":
            print("[UI] Content is a MOVIE, adding movie to download queue")
            self.add_movie_to_download_queue()
            return

        # Get selected episodes (for series only)
        selected_episodes = self.shahid_ui.episodes_list.selectedItems()
        if not selected_episodes:
            QMessageBox.warning(self, "No Episodes Selected", "Please select at least one episode.")
            return

        # Get selected options
        resolution = self.shahid_ui.resolution_combo.currentText()

        # Get content title
        content_title = self.current_content['details']['productModel']['title']

        # Get current season
        current_season_item = self.shahid_ui.seasons_list.currentItem()
        season_data = current_season_item.data(Qt.UserRole)
        season_number = season_data['number']

        # Add each episode to the queue
        for episode_item in selected_episodes:
            episode_data = episode_item.data(Qt.UserRole)
            episode_id = episode_data['id']
            episode_number = episode_data['number']

            # For each episode, we need to get its specific MPD URL
            print(f"[UI] Getting playout URL for episode {episode_number} (ID: {episode_id})")
            print(f"[UI] Processing episode {episode_number} of {len(selected_episodes)} selected episodes")

            # Get episode-specific MPD URL
            episode_mpd_url = None
            episode_drm_info = None

            # Determine which codec to use
            codec = "H.264" if self.shahid_ui.h264_checkbox.isChecked() else "H.265"

            try:
                # Get playout URL for this specific episode
                print(f"[UI] Requesting playout URL for episode ID: {episode_id}")
                playout_data = self.shahid_api.get_episode_playout_url(episode_id)

                # Print a summary of the playout data for debugging
                print(f"[UI] Received playout data for episode {episode_number} (ID: {episode_id})")

                if playout_data:
                    # Check if we have MPD URLs in the playout data
                    if 'mpd_urls' in playout_data and playout_data['mpd_urls']:
                        print(f"[UI] Found {len(playout_data['mpd_urls'])} MPD URLs in playout data")

                        # Use the codec determined earlier
                        codec_key = 'H264' if codec == 'H.264' else 'H265'

                        # Get the MPD URL based on the selected codec
                        if codec_key in playout_data.get('mpd_urls', {}):
                            episode_mpd_url = playout_data['mpd_urls'][codec_key]
                            print(f"[UI] Using MPD URL for {codec_key}: {episode_mpd_url}")
                        else:
                            print(f"[UI] No MPD URL found for codec {codec_key} in episode {episode_number}")
                            # Try the other codec as fallback
                            fallback_codec_key = 'H265' if codec_key == 'H264' else 'H264'
                            if fallback_codec_key in playout_data.get('mpd_urls', {}):
                                episode_mpd_url = playout_data['mpd_urls'][fallback_codec_key]
                                print(f"[UI] Using fallback MPD URL for {fallback_codec_key}: {episode_mpd_url}")
                                # Update the codec to match the fallback
                                codec = "H.265" if fallback_codec_key == 'H265' else "H.264"
                            else:
                                print(f"[UI] No MPD URL found for any codec in episode {episode_number}")
                                continue  # Skip this episode
                    else:
                        print(f"[UI] No MPD URLs found in playout data for episode {episode_number}")
                        continue  # Skip this episode

                    # If we have a valid MPD URL, extract DRM info
                    if episode_mpd_url:
                        print(f"[UI] Got MPD URL for episode {episode_number}: {episode_mpd_url}")

                        # Extract DRM info for this episode
                        print(f"[UI] Extracting DRM info for episode {episode_number} (ID: {episode_id})")
                        try:
                            # Try using shahid_drm first
                            if hasattr(self, 'shahid_drm') and self.shahid_drm:
                                episode_drm_info = self.shahid_drm.process_episode_drm(episode_id, episode_mpd_url)
                                if episode_drm_info:
                                    print(f"[UI] Successfully extracted DRM info for episode {episode_number}")
                                    print(f"[UI] DRM KID: {episode_drm_info.get('kid', 'N/A')}")
                                    print(f"[UI] DRM KEY: {episode_drm_info.get('formatted_key', 'N/A')}")
                                else:
                                    print(f"[UI] Failed to extract DRM info for episode {episode_number}")
                            else:
                                # Fallback: Extract PSSH and KID directly from MPD
                                print(f"[UI] DRM module not available, extracting PSSH/KID directly from MPD")
                                episode_drm_info = self.extract_drm_from_mpd(episode_mpd_url)
                                if episode_drm_info:
                                    print(f"[UI] Successfully extracted DRM info directly from MPD")
                                    print(f"[UI] DRM PSSH: {episode_drm_info.get('pssh', 'N/A')}")
                                    print(f"[UI] DRM KID: {episode_drm_info.get('kid', 'N/A')}")
                                else:
                                    print(f"[UI] Failed to extract DRM info from MPD")
                        except Exception as e:
                            print(f"[UI] Error extracting DRM info for episode {episode_number}: {e}")
                            import traceback
                            traceback.print_exc()
                            # Continue even if DRM extraction fails - we'll try again later
                    else:
                        print(f"[UI] Failed to get MPD URL for episode {episode_number}")
                        continue  # Skip this episode
                else:
                    print(f"[UI] Failed to get playout data for episode {episode_number}")
                    continue  # Skip this episode
            except Exception as e:
                print(f"[UI] Error getting playout URL for episode {episode_number}: {e}")
                continue  # Skip this episode

            # Add to downloads table
            row_position = self.shahid_ui.downloads_table.rowCount()
            self.shahid_ui.downloads_table.insertRow(row_position)

            # Set table items with center alignment
            title_item = QTableWidgetItem(content_title)
            title_item.setTextAlignment(Qt.AlignCenter)
            self.shahid_ui.downloads_table.setItem(row_position, 0, title_item)

            season_item = QTableWidgetItem(str(season_number))
            season_item.setTextAlignment(Qt.AlignCenter)
            self.shahid_ui.downloads_table.setItem(row_position, 1, season_item)

            episode_item = QTableWidgetItem(str(episode_number))
            episode_item.setTextAlignment(Qt.AlignCenter)
            self.shahid_ui.downloads_table.setItem(row_position, 2, episode_item)

            resolution_item = QTableWidgetItem(resolution)
            resolution_item.setTextAlignment(Qt.AlignCenter)
            self.shahid_ui.downloads_table.setItem(row_position, 3, resolution_item)

            # Set codec with center alignment
            codec = "H.264" if self.shahid_ui.h264_checkbox.isChecked() else "H.265"
            codec_item = QTableWidgetItem(codec)
            codec_item.setTextAlignment(Qt.AlignCenter)
            self.shahid_ui.downloads_table.setItem(row_position, 4, codec_item)

            # Create progress bar for the Progress column
            progress_bar = QProgressBar()
            progress_bar.setRange(0, 100)
            progress_bar.setValue(0)
            progress_bar.setTextVisible(True)
            progress_bar.setAlignment(Qt.AlignCenter)
            progress_bar.setFixedHeight(25)  # Set fixed height for better appearance
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #44475a;
                    border-radius: 5px;
                    text-align: center;
                    background-color: #282a36;
                    color: white;
                    font-weight: bold;
                }
                QProgressBar::chunk {
                    background-color: #50fa7b;
                    border-radius: 2px;
                    margin: 0px;
                    width: 1px;
                }
            """)

            # Set a custom property to track the last update time
            progress_bar.setProperty("last_update_time", time.time())
            self.shahid_ui.downloads_table.setCellWidget(row_position, 5, progress_bar)

            # Set status
            status_item = QTableWidgetItem("Queued")
            status_item.setTextAlignment(Qt.AlignCenter)
            self.shahid_ui.downloads_table.setItem(row_position, 6, status_item)

            # Add cancel button
            buttons_widget = QWidget()
            buttons_layout = QHBoxLayout(buttons_widget)
            buttons_layout.setContentsMargins(0, 0, 0, 0)
            buttons_layout.setAlignment(Qt.AlignCenter)

            cancel_button = QPushButton("Cancel")
            cancel_button.setStyleSheet(
                "background-color: #ff5555; color: white; padding: 5px;"
            )
            cancel_button.clicked.connect(lambda _, row=row_position: self.cancel_download(row))
            buttons_layout.addWidget(cancel_button)

            self.shahid_ui.downloads_table.setCellWidget(row_position, 7, buttons_widget)

            # Store episode data for download
            title_item.setData(Qt.UserRole, {
                'content_id': episode_id,
                'content_title': content_title,
                'content_type': 'EPISODE',
                'season_number': season_number,
                'episode_number': episode_number,
                'mpd_url': episode_mpd_url,
                'resolution': resolution,
                'codec': codec,
                'drm_info': episode_drm_info
            })

            print(f"[UI] Added episode {episode_number} to download queue")

        # Show success message
        QMessageBox.information(self, "Episodes Added", f"Added {len(selected_episodes)} episodes to download queue.")

        # Switch to downloads tab
        if hasattr(self.shahid_ui, 'content_tabs'):
            self.shahid_ui.content_tabs.setCurrentIndex(3)  # Downloads tab

    def add_movie_to_download_queue(self):
        """Add movie to download queue."""
        from PySide6.QtWidgets import QMessageBox, QTableWidgetItem, QProgressBar, QPushButton, QWidget, QHBoxLayout
        from PySide6.QtCore import Qt
        import time

        print("[UI] Adding movie to download queue")

        # Get movie details
        content_details = self.current_content['details']
        product = content_details['productModel']
        movie_id = product.get('id')
        movie_title = product.get('title', 'Unknown Movie')

        # Get selected resolution
        resolution = self.shahid_ui.resolution_combo.currentText()

        # Determine which codec to use
        if self.shahid_ui.h264_checkbox.isChecked():
            codec = "H264"  # Use H264 without dot for consistency with API data
        else:
            codec = "H265"  # Use H265 without dot for consistency with API data

        # Get MPD URL for the movie
        movie_mpd_url = None
        movie_drm_info = None

        # Get the MPD URL from the current playout data
        if hasattr(self, 'current_playout_data') and self.current_playout_data:
            mpd_urls = self.current_playout_data.get('mpd_urls', {})
            if mpd_urls and codec in mpd_urls:
                movie_mpd_url = mpd_urls[codec]
                print(f"[UI] Found MPD URL for {codec}: {movie_mpd_url}")

                # Get DRM info for this movie
                movie_drm_info = self.current_playout_data.get('drm_info')
                if movie_drm_info:
                    print(f"[UI] DRM info obtained for movie: {movie_drm_info['formatted_key']}")
                else:
                    print(f"[UI] No DRM info found for movie {movie_id}")
            else:
                print(f"[UI] No MPD URLs found in playout data or codec {codec} not available")
                print(f"[UI] Available MPD URLs: {mpd_urls}")
                print(f"[UI] Available codecs: {list(mpd_urls.keys()) if mpd_urls else 'None'}")

        if not movie_mpd_url:
            QMessageBox.warning(self, "Error", f"Could not find MPD URL for {codec}")
            return

        # Add movie to downloads table
        row_position = self.shahid_ui.downloads_table.rowCount()
        self.shahid_ui.downloads_table.insertRow(row_position)

        # Title
        title_item = QTableWidgetItem(f"{movie_title}")
        title_item.setData(Qt.UserRole, {
            'content_id': movie_id,
            'content_title': movie_title,
            'content_type': 'MOVIE',
            'season_number': None,
            'episode_number': None,
            'mpd_url': movie_mpd_url,
            'resolution': resolution,
            'codec': codec,
            'drm_info': movie_drm_info
        })
        self.shahid_ui.downloads_table.setItem(row_position, 0, title_item)

        # Season (N/A for movies)
        self.shahid_ui.downloads_table.setItem(row_position, 1, QTableWidgetItem("N/A"))

        # Episode (N/A for movies)
        self.shahid_ui.downloads_table.setItem(row_position, 2, QTableWidgetItem("N/A"))

        # Resolution
        self.shahid_ui.downloads_table.setItem(row_position, 3, QTableWidgetItem(resolution))

        # Codec
        self.shahid_ui.downloads_table.setItem(row_position, 4, QTableWidgetItem(codec))

        # Progress bar
        progress_bar = QProgressBar()
        progress_bar.setValue(0)
        progress_bar.setFormat("Queued")
        self.shahid_ui.downloads_table.setCellWidget(row_position, 5, progress_bar)

        # Status
        self.shahid_ui.downloads_table.setItem(row_position, 6, QTableWidgetItem("Queued"))

        print(f"[UI] Added movie '{movie_title}' to download queue")

        # Show success message
        QMessageBox.information(self, "Movie Added", f"Added '{movie_title}' to download queue.")

        # Switch to downloads tab
        if hasattr(self.shahid_ui, 'content_tabs'):
            self.shahid_ui.content_tabs.setCurrentIndex(3)  # Downloads tab

    def cancel_download(self, row):
        """Cancel a download."""
        from PySide6.QtWidgets import QMessageBox
        reply = QMessageBox.question(
            self,
            "Cancel Download",
            "Are you sure you want to cancel this download?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Remove the row from the table
            self.shahid_ui.downloads_table.removeRow(row)
            print(f"[UI] Cancelled download for row {row}")

    def select_all_options(self):
        """Select all download options."""
        if hasattr(self.shahid_ui, 'h264_checkbox'):
            self.shahid_ui.h264_checkbox.setChecked(True)
        if hasattr(self.shahid_ui, 'h265_checkbox'):
            self.shahid_ui.h265_checkbox.setChecked(True)
        if hasattr(self.shahid_ui, 'audio_ar_checkbox'):
            self.shahid_ui.audio_ar_checkbox.setChecked(True)
        if hasattr(self.shahid_ui, 'audio_en_checkbox'):
            self.shahid_ui.audio_en_checkbox.setChecked(True)
        if hasattr(self.shahid_ui, 'audio_tr_checkbox'):
            self.shahid_ui.audio_tr_checkbox.setChecked(True)

        # Select all subtitles in the list
        if hasattr(self.shahid_ui, 'subtitle_list_widget'):
            for i in range(self.shahid_ui.subtitle_list_widget.count()):
                item = self.shahid_ui.subtitle_list_widget.item(i)
                item.setSelected(True)

        # Update hidden checkboxes
        if hasattr(self.shahid_ui, 'subtitle_ar_checkbox'):
            self.shahid_ui.subtitle_ar_checkbox.setChecked(True)
        if hasattr(self.shahid_ui, 'subtitle_en_checkbox'):
            self.shahid_ui.subtitle_en_checkbox.setChecked(True)

    def select_none_options(self):
        """Deselect all download options."""
        # Deselect codec checkboxes
        if hasattr(self.shahid_ui, 'h264_checkbox'):
            self.shahid_ui.h264_checkbox.setChecked(False)
        if hasattr(self.shahid_ui, 'h265_checkbox'):
            self.shahid_ui.h265_checkbox.setChecked(False)

        # Deselect audio checkboxes
        if hasattr(self.shahid_ui, 'audio_ar_checkbox') and self.shahid_ui.audio_ar_checkbox is not None:
            self.shahid_ui.audio_ar_checkbox.setChecked(False)
        if hasattr(self.shahid_ui, 'audio_en_checkbox') and self.shahid_ui.audio_en_checkbox is not None:
            self.shahid_ui.audio_en_checkbox.setChecked(False)
        if hasattr(self.shahid_ui, 'audio_tr_checkbox') and self.shahid_ui.audio_tr_checkbox is not None:
            self.shahid_ui.audio_tr_checkbox.setChecked(False)

        # Deselect all subtitles in the list
        if hasattr(self.shahid_ui, 'subtitle_list_widget'):
            for i in range(self.shahid_ui.subtitle_list_widget.count()):
                item = self.shahid_ui.subtitle_list_widget.item(i)
                item.setSelected(False)

        # Update hidden checkboxes
        if hasattr(self.shahid_ui, 'subtitle_ar_checkbox'):
            self.shahid_ui.subtitle_ar_checkbox.setChecked(False)
        if hasattr(self.shahid_ui, 'subtitle_en_checkbox'):
            self.shahid_ui.subtitle_en_checkbox.setChecked(False)

    def download_with_idm(self):
        """Download with IDM functionality."""
        from PySide6.QtWidgets import QMessageBox

        # Check if any codec is selected
        if not (self.shahid_ui.h264_checkbox.isChecked() or self.shahid_ui.h265_checkbox.isChecked()):
            QMessageBox.warning(self, "No Codec Selected", "Please select at least one codec.")
            return

        # For movies, skip episode selection check
        if hasattr(self, 'current_content') and self.current_content['type'] == "MOVIE":
            print("[UI] Content is a MOVIE, skipping episode selection check for IDM download")
            # Get selected options for movie
            resolution = self.shahid_ui.resolution_combo.currentText()
            QMessageBox.information(self, "IDM Download",
                                  f"IDM download functionality will be implemented soon.\n"
                                  f"Movie download at {resolution}")
            return

        # Get selected episodes (for series only)
        selected_episodes = self.shahid_ui.episodes_list.selectedItems()
        if not selected_episodes:
            QMessageBox.warning(self, "No Episodes Selected", "Please select at least one episode.")
            return

        # Get selected options
        resolution = self.shahid_ui.resolution_combo.currentText()

        # Show info message
        QMessageBox.information(self, "IDM Download",
                              f"IDM download functionality will be implemented soon.\n"
                              f"Selected: {len(selected_episodes)} episodes at {resolution}")

    def start_downloads(self):
        """Start all queued downloads."""
        from PySide6.QtWidgets import QMessageBox

        # Check if there are any downloads in the queue
        if self.shahid_ui.downloads_table.rowCount() == 0:
            QMessageBox.warning(self, "No Downloads", "No downloads in queue.")
            return

        # Show confirmation
        reply = QMessageBox.question(
            self,
            "Start Downloads",
            f"Start downloading {self.shahid_ui.downloads_table.rowCount()} items?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )

        if reply == QMessageBox.Yes:
            print(f"[UI] Starting {self.shahid_ui.downloads_table.rowCount()} downloads")

            # Process each download in the queue
            for row in range(self.shahid_ui.downloads_table.rowCount()):
                # Get download data
                title_item = self.shahid_ui.downloads_table.item(row, 0)
                if title_item:
                    download_data = title_item.data(Qt.UserRole)
                    if download_data:
                        # Update status to "Downloading"
                        status_item = self.shahid_ui.downloads_table.item(row, 6)
                        if status_item:
                            status_item.setText("Downloading")

                        # Start the actual download process using shahid_downloader
                        self.start_actual_download(row, download_data)

    def start_actual_download(self, row, download_data):
        """Start the actual download using ShahidDownloader."""
        try:
            if not self.shahid_downloader:
                print(f"[UI] Error: ShahidDownloader not available")
                status_item = self.shahid_ui.downloads_table.item(row, 6)
                if status_item:
                    status_item.setText("Error: Downloader not available")
                return

            # Extract download information
            content_id = download_data.get('content_id')
            content_title = download_data.get('content_title')
            content_type = download_data.get('content_type')
            season_number = download_data.get('season_number')
            episode_number = download_data.get('episode_number')
            mpd_url = download_data.get('mpd_url')
            resolution = download_data.get('resolution')
            codec = download_data.get('codec')
            drm_info = download_data.get('drm_info')

            print(f"[UI] Starting actual download for {content_title}")
            print(f"[UI] Content Type: {content_type}")
            print(f"[UI] Resolution: {resolution}, Codec: {codec}")
            print(f"[UI] MPD URL: {mpd_url}")

            # Get selected audio and subtitle tracks
            selected_audio = self.get_selected_audio_tracks()
            selected_subtitles = self.get_selected_subtitle_tracks()

            print(f"[UI] Selected audio tracks: {selected_audio}")
            print(f"[UI] Selected subtitle tracks: {selected_subtitles}")

            # Connect progress signals
            if hasattr(self.shahid_downloader, 'signals'):
                # Connect progress update signal
                self.shahid_downloader.signals.progress_updated.connect(
                    lambda row_idx, progress: self.update_download_progress(row_idx, progress)
                )

                # Connect status update signal
                self.shahid_downloader.signals.status_updated.connect(
                    lambda row_idx, status: self.update_download_status(row_idx, status)
                )

                # Connect completion signal
                self.shahid_downloader.signals.download_completed.connect(
                    lambda row_idx, success: self.on_download_completed(row_idx, success)
                )

            # Start the appropriate download based on content type
            if content_type == "MOVIE":
                # Download movie
                self.shahid_downloader.download_movie(
                    row_index=row,
                    mpd_url=mpd_url,
                    resolution=resolution,
                    audio_tracks=selected_audio,
                    subtitle_tracks=selected_subtitles,
                    movie_title=content_title,
                    drm_info=drm_info
                )
            elif content_type == "EPISODE":
                # Download episode using the correct function name
                self.shahid_downloader.download_series_with_selected_quality(
                    row_index=row,
                    mpd_url=mpd_url,
                    resolution=resolution,
                    audio_tracks=selected_audio,
                    subtitle_tracks=selected_subtitles,
                    series_title=content_title,
                    season_number=season_number,
                    episode_number=episode_number,
                    drm_info=drm_info
                )
            else:
                print(f"[UI] Unknown content type: {content_type}")
                status_item = self.shahid_ui.downloads_table.item(row, 6)
                if status_item:
                    status_item.setText("Error: Unknown content type")

        except Exception as e:
            print(f"[UI] Error starting actual download: {e}")
            import traceback
            traceback.print_exc()
            status_item = self.shahid_ui.downloads_table.item(row, 6)
            if status_item:
                status_item.setText("Error")

    def update_download_progress(self, row, progress):
        """Update download progress in the table."""
        try:
            progress_bar = self.shahid_ui.downloads_table.cellWidget(row, 5)
            if progress_bar:
                progress_bar.setValue(progress)
                progress_bar.setFormat(f"{progress}%")
        except Exception as e:
            print(f"[UI] Error updating progress: {e}")

    def update_download_status(self, row, status):
        """Update download status in the table."""
        try:
            status_item = self.shahid_ui.downloads_table.item(row, 6)
            if status_item:
                status_item.setText(status)
        except Exception as e:
            print(f"[UI] Error updating status: {e}")

    def on_download_completed(self, row, success):
        """Handle download completion."""
        try:
            if success:
                print(f"[UI] Download completed successfully for row {row}")
                status_item = self.shahid_ui.downloads_table.item(row, 6)
                if status_item:
                    status_item.setText("Completed")

                progress_bar = self.shahid_ui.downloads_table.cellWidget(row, 5)
                if progress_bar:
                    progress_bar.setValue(100)
                    progress_bar.setFormat("Completed")

                # Play completion sound if available
                try:
                    from pathlib import Path
                    yango_root = Path(__file__).parent.parent.parent
                    sound_path = yango_root / "binaries" / "alert.wav"
                    if sound_path.exists():
                        import winsound
                        winsound.PlaySound(str(sound_path), winsound.SND_FILENAME | winsound.SND_ASYNC)
                except Exception as sound_error:
                    print(f"[UI] Could not play completion sound: {sound_error}")
            else:
                print(f"[UI] Download failed for row {row}")
                status_item = self.shahid_ui.downloads_table.item(row, 6)
                if status_item:
                    status_item.setText("Failed")

        except Exception as e:
            print(f"[UI] Error handling download completion: {e}")

    def process_download(self, row, download_data):
        """Process a single download."""
        print(f"[UI] Processing download for row {row}")
        print(f"[UI] Download data: {download_data}")

        try:
            # Extract download information
            content_id = download_data.get('content_id')
            content_title = download_data.get('content_title')
            content_type = download_data.get('content_type')
            season_number = download_data.get('season_number')
            episode_number = download_data.get('episode_number')
            mpd_url = download_data.get('mpd_url')
            resolution = download_data.get('resolution')
            codec = download_data.get('codec')
            drm_info = download_data.get('drm_info')

            print(f"[UI] Starting download for {content_title} S{season_number}E{episode_number}")
            print(f"[UI] Resolution: {resolution}, Codec: {codec}")
            print(f"[UI] MPD URL: {mpd_url}")

            # Get selected audio and subtitle options
            selected_audio = self.get_selected_audio_tracks()
            selected_subtitles = self.get_selected_subtitle_tracks()

            print(f"[UI] Selected audio tracks: {selected_audio}")
            print(f"[UI] Selected subtitle tracks: {selected_subtitles}")

            # Update progress bar to show starting
            progress_bar = self.shahid_ui.downloads_table.cellWidget(row, 5)
            if progress_bar:
                progress_bar.setValue(5)
                progress_bar.setFormat("Starting...")

            # Here you would call the actual download function
            # For now, simulate download progress
            self.simulate_download_progress(row, download_data)

        except Exception as e:
            print(f"[UI] Error processing download: {e}")
            # Update status to "Error"
            status_item = self.shahid_ui.downloads_table.item(row, 6)
            if status_item:
                status_item.setText("Error")

    def simulate_download_progress(self, row, download_data):
        """Simulate download progress for testing."""
        from PySide6.QtCore import QTimer
        import random

        # Get progress bar
        progress_bar = self.shahid_ui.downloads_table.cellWidget(row, 5)
        if not progress_bar:
            return

        # Create timer for progress updates
        timer = QTimer()
        current_progress = [5]  # Use list to allow modification in nested function

        def update_progress():
            if current_progress[0] < 100:
                # Simulate realistic download progress
                increment = random.randint(1, 5)
                current_progress[0] = min(100, current_progress[0] + increment)
                progress_bar.setValue(current_progress[0])
                progress_bar.setFormat(f"{current_progress[0]}%")

                # Update status based on progress
                if current_progress[0] < 30:
                    progress_bar.setFormat("Downloading video...")
                elif current_progress[0] < 60:
                    progress_bar.setFormat("Downloading audio...")
                elif current_progress[0] < 90:
                    progress_bar.setFormat("Downloading subtitles...")
                else:
                    progress_bar.setFormat("Finalizing...")
            else:
                # Download completed
                timer.stop()
                progress_bar.setValue(100)
                progress_bar.setFormat("Completed")

                # Update status
                status_item = self.shahid_ui.downloads_table.item(row, 6)
                if status_item:
                    status_item.setText("Completed")

                print(f"[UI] Download completed for row {row}")

        timer.timeout.connect(update_progress)
        timer.start(1000)  # Update every second

    def get_selected_audio_tracks(self):
        """Get selected audio tracks."""
        selected_audio = []

        # Check audio checkboxes in the layout
        if hasattr(self.shahid_ui, 'audio_group_layout'):
            for i in range(self.shahid_ui.audio_group_layout.count()):
                item = self.shahid_ui.audio_group_layout.itemAt(i)
                if item and item.widget():
                    widget = item.widget()
                    if hasattr(widget, 'isChecked') and widget.isChecked():
                        text = widget.text()
                        # Extract language code from text like "Arabic (ar)"
                        if '(' in text and ')' in text:
                            code = text.split('(')[1].split(')')[0]
                            selected_audio.append(code)

        return selected_audio

    def get_selected_subtitle_tracks(self):
        """Get selected subtitle tracks."""
        selected_subtitles = []

        # Check subtitle list widget
        if hasattr(self.shahid_ui, 'subtitle_list_widget'):
            for i in range(self.shahid_ui.subtitle_list_widget.count()):
                item = self.shahid_ui.subtitle_list_widget.item(i)
                if item and item.isSelected():
                    text = item.text()
                    # Extract language code from text like "Arabic (ar)"
                    if '(' in text and ')' in text:
                        code = text.split('(')[1].split(')')[0]
                        selected_subtitles.append(code)

        return selected_subtitles

    def clear_completed_downloads(self):
        """Clear completed downloads from the queue."""
        from PySide6.QtWidgets import QMessageBox

        rows_to_remove = []

        # Find completed downloads
        for row in range(self.shahid_ui.downloads_table.rowCount()):
            status_item = self.shahid_ui.downloads_table.item(row, 6)
            if status_item and status_item.text() == "Completed":
                rows_to_remove.append(row)

        if not rows_to_remove:
            QMessageBox.information(self, "No Completed Downloads", "No completed downloads to clear.")
            return

        # Remove rows in reverse order to maintain indices
        for row in reversed(rows_to_remove):
            self.shahid_ui.downloads_table.removeRow(row)

        QMessageBox.information(self, "Cleared", f"Cleared {len(rows_to_remove)} completed downloads.")

    def clear_all_downloads(self):
        """Clear all downloads from the queue."""
        from PySide6.QtWidgets import QMessageBox

        if self.shahid_ui.downloads_table.rowCount() == 0:
            QMessageBox.information(self, "No Downloads", "No downloads to clear.")
            return

        reply = QMessageBox.question(
            self,
            "Clear All Downloads",
            "Are you sure you want to clear all downloads?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            count = self.shahid_ui.downloads_table.rowCount()
            self.shahid_ui.downloads_table.setRowCount(0)
            QMessageBox.information(self, "Cleared", f"Cleared {count} downloads.")
