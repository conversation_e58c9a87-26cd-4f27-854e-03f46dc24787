#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Browser-based player using real browser instead of QWebEngineView
"""

import os
import sys
import subprocess
import threading
import time
import urllib.parse
import socketserver
import http.server
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel, QFrame
from PySide6.QtCore import Qt, QTimer, Signal, QObject
from PySide6.QtGui import QFont, QWindow
import ctypes
from ctypes import wintypes

# Windows API functions for window embedding
if sys.platform == "win32":
    user32 = ctypes.windll.user32
    kernel32 = ctypes.windll.kernel32

    # Define Windows API functions
    user32.SetParent.argtypes = [wintypes.HWND, wintypes.HWND]
    user32.SetParent.restype = wintypes.HWND

    user32.SetWindowLongW.argtypes = [wintypes.HWND, ctypes.c_int, wintypes.LONG]
    user32.SetWindowLongW.restype = wintypes.LONG

    user32.GetWindowLongW.argtypes = [wintypes.HWND, ctypes.c_int]
    user32.GetWindowLongW.restype = wintypes.LONG

    user32.SetWindowPos.argtypes = [wintypes.HWND, wintypes.HWND, ctypes.c_int, ctypes.c_int, ctypes.c_int, ctypes.c_int, wintypes.UINT]
    user32.SetWindowPos.restype = wintypes.BOOL

    user32.FindWindowW.argtypes = [wintypes.LPCWSTR, wintypes.LPCWSTR]
    user32.FindWindowW.restype = wintypes.HWND

    user32.EnumWindows.argtypes = [ctypes.WINFUNCTYPE(wintypes.BOOL, wintypes.HWND, wintypes.LPARAM), wintypes.LPARAM]
    user32.EnumWindows.restype = wintypes.BOOL

    user32.GetWindowThreadProcessId.argtypes = [wintypes.HWND, ctypes.POINTER(wintypes.DWORD)]
    user32.GetWindowThreadProcessId.restype = wintypes.DWORD

    user32.GetWindowTextLengthW.argtypes = [wintypes.HWND]
    user32.GetWindowTextLengthW.restype = ctypes.c_int

    # Constants
    GWL_STYLE = -16
    WS_CHILD = 0x40000000
    WS_VISIBLE = 0x10000000
    SWP_NOZORDER = 0x0004
    SWP_NOACTIVATE = 0x0010

class BrowserPlayerSignals(QObject):
    """Signals for browser player events."""
    player_started = Signal(bool)
    player_stopped = Signal()
    player_error = Signal(str)

class SimpleHTTPHandler(http.server.SimpleHTTPRequestHandler):
    """Simple HTTP request handler with custom directory."""
    def __init__(self, *args, directory=None, **kwargs):
        self.directory = directory
        super().__init__(*args, directory=directory, **kwargs)

    def log_message(self, format, *args):
        """Override to customize logging."""
        print(f"[HTTP] {format % args}")

    def end_headers(self):
        """Add CORS headers."""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

class BrowserPlayerWidget(QWidget):
    """Widget that embeds a real browser for playing content."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.browser_process = None
        self.http_server = None
        self.server_thread = None
        self.player_url = None
        self.signals = BrowserPlayerSignals()
        self.embedded_hwnd = None
        self.embed_timer = None
        self.parent_widget = parent

        self.setup_ui()

    def setup_ui(self):
        """Setup the UI for the browser player widget."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # Create header with controls
        header = QFrame()
        header.setFixedHeight(60)
        header.setStyleSheet("""
            QFrame {
                background-color: #2b2b2b;
                border-bottom: 2px solid #444;
            }
        """)

        header_layout = QHBoxLayout(header)

        # Title
        title_label = QLabel("🌐 Browser Player")
        title_label.setStyleSheet("color: white; font-size: 16px; font-weight: bold;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Controls
        self.refresh_btn = QPushButton("🔄 Refresh")
        self.refresh_btn.clicked.connect(self.refresh_browser)
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                border: none;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        header_layout.addWidget(self.refresh_btn)

        self.close_btn = QPushButton("❌ Close")
        self.close_btn.clicked.connect(self.close_browser)
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                border: none;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        header_layout.addWidget(self.close_btn)

        layout.addWidget(header)

        # Browser container
        self.browser_container = QFrame()
        self.browser_container.setStyleSheet("""
            QFrame {
                background-color: #000;
                border: 2px solid #444;
            }
        """)

        browser_layout = QVBoxLayout(self.browser_container)

        # Status label
        self.status_label = QLabel("🚀 Starting browser player...")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("""
            color: white;
            font-size: 18px;
            padding: 50px;
            background-color: rgba(0,0,0,0.8);
        """)
        browser_layout.addWidget(self.status_label)

        layout.addWidget(self.browser_container)

    def find_browser(self):
        """Find available browser executable."""
        browsers = []

        if sys.platform == 'win32':
            # Windows browsers
            possible_paths = [
                # Chrome
                os.path.expandvars(r'%PROGRAMFILES%\Google\Chrome\Application\chrome.exe'),
                os.path.expandvars(r'%PROGRAMFILES(X86)%\Google\Chrome\Application\chrome.exe'),
                os.path.expandvars(r'%LOCALAPPDATA%\Google\Chrome\Application\chrome.exe'),
                # Edge
                os.path.expandvars(r'%PROGRAMFILES%\Microsoft\Edge\Application\msedge.exe'),
                os.path.expandvars(r'%PROGRAMFILES(X86)%\Microsoft\Edge\Application\msedge.exe'),
                # Firefox
                os.path.expandvars(r'%PROGRAMFILES%\Mozilla Firefox\firefox.exe'),
                os.path.expandvars(r'%PROGRAMFILES(X86)%\Mozilla Firefox\firefox.exe'),
            ]
        else:
            # Linux/Mac browsers
            possible_paths = [
                '/usr/bin/google-chrome',
                '/usr/bin/chromium-browser',
                '/usr/bin/firefox',
                '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
                '/Applications/Firefox.app/Contents/MacOS/firefox',
            ]

        for path in possible_paths:
            if os.path.exists(path):
                browsers.append(path)

        return browsers

    def start_http_server(self, player_dir):
        """Start HTTP server to serve player files."""
        for port in range(8002, 8010):
            try:
                print(f"Trying to start HTTP server on port {port}...")

                handler = lambda *args, **kwargs: SimpleHTTPHandler(
                    *args, directory=player_dir, **kwargs
                )

                self.http_server = socketserver.TCPServer(("localhost", port), handler)
                self.http_server.allow_reuse_address = True

                self.server_thread = threading.Thread(
                    target=self.http_server.serve_forever,
                    daemon=True
                )
                self.server_thread.start()

                print(f"HTTP server started on port {port}")
                return port

            except OSError as e:
                if "Address already in use" in str(e):
                    continue
                else:
                    print(f"Error starting HTTP server: {e}")
                    break

        return None

    def stop_http_server(self):
        """Stop the HTTP server."""
        if self.http_server:
            try:
                self.http_server.shutdown()
                self.http_server.server_close()
                print("HTTP server stopped")
            except Exception as e:
                print(f"Error stopping HTTP server: {e}")
            finally:
                self.http_server = None
                self.server_thread = None

    def load_player(self, mpd_url, drm_key=None, player_dir=None):
        """Load the player in a real browser."""
        try:
            print(f"🌐 Loading player in real browser...")
            print(f"MPD URL: {mpd_url}")
            print(f"DRM Key: {drm_key}")

            # Start HTTP server
            if not player_dir:
                # Get player directory - go up 3 levels to reach YANGO root
                base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                player_dir = os.path.join(base_dir, 'player-shahid')
                print(f"Player directory path: {player_dir}")
                print(f"Player directory exists: {os.path.exists(player_dir)}")

            port = self.start_http_server(player_dir)
            if not port:
                self.status_label.setText("❌ Failed to start HTTP server")
                return False

            # Create player URL
            query_params = {'mpd': mpd_url}
            if drm_key and ':' in drm_key:
                key_id, key = drm_key.split(':', 1)
                query_params['keyId'] = key_id
                query_params['key'] = key

            self.player_url = f"http://localhost:{port}/shahid_player.html?{urllib.parse.urlencode(query_params)}"
            print(f"Player URL: {self.player_url}")

            # Find and start browser
            browsers = self.find_browser()
            if not browsers:
                self.status_label.setText("❌ No supported browser found")
                return False

            browser_path = browsers[0]  # Use first available browser
            browser_name = os.path.basename(browser_path).replace('.exe', '')

            print(f"Using browser: {browser_name} at {browser_path}")
            self.status_label.setText(f"🚀 Starting {browser_name}...")

            # Browser arguments for embedded mode
            if 'chrome' in browser_name.lower() or 'msedge' in browser_name.lower():
                # Chrome/Edge arguments for embedded mode
                user_data_dir = os.path.join(os.path.expanduser('~'), '.shahid_browser')
                args = [
                    browser_path,
                    '--app=' + self.player_url,
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-popup-blocking',
                    '--allow-running-insecure-content',
                    '--autoplay-policy=no-user-gesture-required',
                    '--allow-file-access-from-files',
                    '--allow-file-access',
                    '--ignore-certificate-errors',
                    '--user-data-dir=' + user_data_dir,
                    '--window-size=991,602',
                    '--disable-extensions',
                    '--disable-default-apps',
                    '--no-sandbox',
                    '--disable-gpu-sandbox'
                ]
            else:
                # Firefox arguments
                args = [
                    browser_path,
                    '--new-window',
                    self.player_url
                ]

            # Start browser process
            self.browser_process = subprocess.Popen(
                args,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )

            self.status_label.setText(f"✅ {browser_name} started successfully!")

            # Monitor browser process
            self.monitor_browser()

            # Keep browser window separate - don't embed it
            print("🌐 Browser window will remain as separate window")

            self.signals.player_started.emit(True)
            return True

        except Exception as e:
            error_msg = f"Error starting browser player: {e}"
            print(f"❌ {error_msg}")
            self.status_label.setText(f"❌ {error_msg}")
            self.signals.player_error.emit(error_msg)
            return False

    def monitor_browser(self):
        """Monitor browser process."""
        def check_process():
            if self.browser_process and self.browser_process.poll() is not None:
                print("Browser process ended")
                self.status_label.setText("🔄 Browser closed")
                self.signals.player_stopped.emit()
                self.browser_process = None

        # Check every 2 seconds
        timer = QTimer(self)
        timer.timeout.connect(check_process)
        timer.start(2000)

    def refresh_browser(self):
        """Refresh the browser."""
        if self.player_url:
            self.close_browser()
            time.sleep(1)
            # Restart with same URL
            # This would need the original parameters, simplified for now
            print("Refreshing browser...")

    def close_browser(self):
        """Close the browser and cleanup."""
        if self.browser_process:
            try:
                self.browser_process.terminate()
                self.browser_process.wait(timeout=5)
                print("Browser process terminated")
            except subprocess.TimeoutExpired:
                self.browser_process.kill()
                print("Browser process killed")
            except Exception as e:
                print(f"Error closing browser: {e}")
            finally:
                self.browser_process = None

        self.stop_http_server()
        self.status_label.setText("🔄 Browser closed")
        self.signals.player_stopped.emit()

    def start_embed_timer(self):
        """Start timer to find and embed browser window."""
        if sys.platform != "win32":
            return

        self.embed_timer = QTimer(self)
        self.embed_timer.timeout.connect(self.try_embed_browser)
        self.embed_timer.start(1000)  # Try every second
        print("🔍 Started embed timer to find browser window...")

    def try_embed_browser(self):
        """Try to find and embed the browser window."""
        if not self.browser_process or sys.platform != "win32":
            return

        try:
            # Find browser window by process ID
            browser_hwnd = self.find_browser_window()
            if browser_hwnd:
                print(f"🎯 Found browser window: {browser_hwnd}")
                success = self.embed_window(browser_hwnd)
                if success:
                    print("✅ Browser window embedded successfully!")
                    self.status_label.setText("✅ Browser embedded in application!")
                    if self.embed_timer:
                        self.embed_timer.stop()
                        self.embed_timer = None
                else:
                    print("❌ Failed to embed browser window")
            else:
                print("🔍 Browser window not found yet...")

        except Exception as e:
            print(f"❌ Error in try_embed_browser: {e}")

    def find_browser_window(self):
        """Find the browser window by process ID."""
        if not self.browser_process or sys.platform != "win32":
            return None

        browser_pid = self.browser_process.pid
        found_hwnd = None

        def enum_windows_callback(hwnd, lparam):
            nonlocal found_hwnd
            try:
                # Get window process ID
                process_id = wintypes.DWORD()
                user32.GetWindowThreadProcessId(hwnd, ctypes.byref(process_id))

                if process_id.value == browser_pid:
                    # Check if this is a main window (has title)
                    title_length = user32.GetWindowTextLengthW(hwnd)
                    if title_length > 0:
                        found_hwnd = hwnd
                        return False  # Stop enumeration

            except Exception as e:
                print(f"Error in enum callback: {e}")

            return True  # Continue enumeration

        try:
            # Enumerate all windows
            enum_func = ctypes.WINFUNCTYPE(wintypes.BOOL, wintypes.HWND, wintypes.LPARAM)(enum_windows_callback)
            user32.EnumWindows(enum_func, 0)
            return found_hwnd

        except Exception as e:
            print(f"Error finding browser window: {e}")
            return None

    def embed_window(self, hwnd):
        """Embed the browser window into this widget."""
        if sys.platform != "win32":
            return False

        try:
            # Get this widget's window handle
            parent_hwnd = int(self.browser_container.winId())

            print(f"🔧 Embedding window {hwnd} into parent {parent_hwnd}")

            # Set the browser window as child of this widget
            result = user32.SetParent(hwnd, parent_hwnd)
            if not result:
                print("❌ SetParent failed")
                return False

            # Modify window style to be a child window
            current_style = user32.GetWindowLongW(hwnd, GWL_STYLE)
            new_style = (current_style | WS_CHILD | WS_VISIBLE) & ~0x00C00000  # Remove WS_CAPTION
            user32.SetWindowLongW(hwnd, GWL_STYLE, new_style)

            # Position and size the window to fill the container
            container_rect = self.browser_container.rect()
            user32.SetWindowPos(
                hwnd, 0, 0, 0,
                container_rect.width(), container_rect.height(),
                SWP_NOZORDER | SWP_NOACTIVATE
            )

            self.embedded_hwnd = hwnd
            print("✅ Window embedded successfully!")

            # Hide the status label since we now have the browser
            self.status_label.hide()

            return True

        except Exception as e:
            print(f"❌ Error embedding window: {e}")
            return False

    def resizeEvent(self, event):
        """Handle resize events."""
        super().resizeEvent(event)
        # No need to resize embedded window since we're not embedding

    def closeEvent(self, event):
        """Handle widget close event."""
        # No need to restore window since we're not embedding it
        self.close_browser()
        super().closeEvent(event)

# Function to create browser player widget
def create_browser_player(mpd_url, drm_key=None, parent=None):
    """
    Create a browser player widget.

    Args:
        mpd_url (str): The URL of the MPD file to play
        drm_key (str, optional): The DRM key for decryption in the format "kid:key"
        parent (QWidget, optional): Parent widget for the player

    Returns:
        BrowserPlayerWidget: The player widget, or None if failed
    """
    try:
        print("create_browser_player called")
        widget = BrowserPlayerWidget(parent)

        # Get player directory - go up 3 levels to reach YANGO root
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        player_dir = os.path.join(base_dir, 'player-shahid')
        print(f"Player directory path: {player_dir}")
        print(f"Player directory exists: {os.path.exists(player_dir)}")

        success = widget.load_player(mpd_url, drm_key, player_dir)

        if success:
            print("Browser player created successfully")
            return widget
        else:
            print("Failed to create browser player")
            return None

    except Exception as e:
        print(f"Error in create_browser_player: {e}")
        import traceback
        traceback.print_exc()
        return None
