# -*- coding: utf-8 -*-

import sys
import os
import subprocess

exit_code = subprocess.call(
    ['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python.exe', '-W', 'ignore', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\INLINE~1\\bin\\scons.py', '--quiet', '-f', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\BACKEN~1.SCO', '--jobs', '16', '--warn=no-deprecated', '--no-site-dir', 'nuitka_src=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build', 'python_version=3.9', 'python_prefix=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39', 'experimental=', 'debug_modes=', 'deployment=false', 'no_deployment=', 'assume_yes_for_downloads=true', 'console_mode=disable', 'noelf_mode=true', 'target_arch=x86_64', 'module_mode=false', 'dll_mode=true', 'exe_mode=false', 'standalone_mode=true', 'onefile_mode=true', 'onefile_temp_mode=true', 'gil_mode=true', 'source_dir=.', 'nuitka_python=false', 'debug_mode=false', 'debugger_mode=false', 'python_debug=false', 'full_compat=false', 'trace_mode=false', 'file_reference_mode=runtime', 'compiled_module_count=317', 'result_exe=C:\\Users\\<USER>\\OneDrive\\Desktop\\NE86E9~1\\YANGO\\dist\\MAIN~1.DIS\\main.dll', 'frozen_modules=153', 'python_sysflag_no_site=true'],
    env={'ALLUSERSPROFILE': 'C:\\ProgramData','APPDATA': 'C:\\Users\\<USER>\\AppData\\Roaming','ASL.LOG': 'Destination=file','CAPTCHA_API_KEY': '6Ldi0UArAAAAANSE8WLMzy8Zr5StttasKW5FlDAN','CHROME_CRASHPAD_PIPE_NAME': '\\\\.\\pipe\\crashpad_14504_TXZZVWGDGVKVZWFJ','COMMONPROGRAMFILES': 'C:\\Program Files\\Common Files','COMMONPROGRAMFILES(X86)': 'C:\\Program Files (x86)\\Common Files','COMMONPROGRAMW6432': 'C:\\Program Files\\Common Files','COMPUTERNAME': 'TEEFA','COMSPEC': 'C:\\WINDOWS\\system32\\cmd.exe','DRIVERDATA': 'C:\\Windows\\System32\\Drivers\\DriverData','EFC_8600_1592913036': '1','HOMEDRIVE': 'C:','HOMEPATH': '\\Users\\musta','LOCALAPPDATA': 'C:\\Users\\<USER>\\AppData\\Local','LOGONSERVER': '\\\\TEEFA','NUMBER_OF_PROCESSORS': '16','ONEDRIVE': 'C:\\Users\\<USER>\\OneDrive','ORIGINAL_XDG_CURRENT_DESKTOP': 'undefined','OS': 'Windows_NT','PATH': 'C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\java8path;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\nodejs\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\dotnet\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files (x86)\\dotnet\\C:\\ProgramData\\ComposerSetup\\binC:\\ProgramData\\ComposerSetup\\binC:\\ProgramData\\ComposerSetup\\binC:\\ProgramData\\ComposerSetup\\binC:\\ProgramData\\ComposerSetup\\binC:\\ProgramData\\ComposerSetup\\binC:\\composerC:\\ProgramData\\ComposerSetup\\bin;C:\\MAMP\\bin\\php\\php8.2.19;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Program Files\\mitmproxy\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Program Files\\OpenSSL-Win64\\bin;C:\\Users\\<USER>\\AppData\\Local\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Links;;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand','PATHEXT': '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL','PROCESSOR_ARCHITECTURE': 'AMD64','PROCESSOR_IDENTIFIER': 'Intel64 Family 6 Model 191 Stepping 2, GenuineIntel','PROCESSOR_LEVEL': '6','PROCESSOR_REVISION': 'bf02','PROGRAMDATA': 'C:\\ProgramData','PROGRAMFILES': 'C:\\Program Files','PROGRAMFILES(X86)': 'C:\\Program Files (x86)','PROGRAMW6432': 'C:\\Program Files','PSMODULEPATH': 'C:\\Users\\<USER>\\OneDrive\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules','PUBLIC': 'C:\\Users\\<USER>\\WINDOWS','TEMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','TMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','USERDOMAIN': 'TEEFA','USERDOMAIN_ROAMINGPROFILE': 'TEEFA','USERNAME': 'musta','USERPROFILE': 'C:\\Users\\<USER>\\WINDOWS','WSA_PACMAN_HOME': 'C:\\Program Files\\WSA PacMan','LANG': 'en_US.UTF-8','PYDEVD_DISABLE_FILE_VALIDATION': '1','BUNDLED_DEBUGPY_PATH': 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy','NUITKA_PYTHON_EXE_PATH': 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python.exe','NUITKA_PACKAGE_DIR': 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages\\nuitka','NUITKA_CACHE_DIR_DOWNLOADS': 'C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1','_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT': '5000','_NUITKA_BUILD_DEFINITIONS_CATALOG': '_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT,_NUITKA_BUILD_DEFINITIONS_CATALOG','NUITKA_QUIET': '0'},
    shell=False
)