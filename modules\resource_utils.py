"""
Resource utilities for YANGO application
Handles resource paths for both development and compiled executable modes
"""

import os
import sys
from pathlib import Path


def get_resource_path(relative_path):
    """
    Get absolute path to resource, works for dev and for PyInstaller/Nuitka

    Args:
        relative_path (str): Relative path to the resource

    Returns:
        str: Absolute path to the resource
    """
    try:
        # Check if running as Nuitka onefile
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller mode
            base_path = sys._MEIPASS
            print(f"🔧 Using PyInstaller mode base path: {base_path}")
        elif '__compiled__' in globals():
            # Nuitka compiled mode
            base_path = os.path.dirname(sys.executable)
            print(f"🔧 Using Nuitka compiled mode base path: {base_path}")
        else:
            # Try to detect <PERSON><PERSON><PERSON> by checking if we're in a compiled environment
            if getattr(sys, 'frozen', False):
                # Frozen executable (Nuitka or PyInstaller)
                base_path = os.path.dirname(sys.executable)
                print(f"🔧 Using frozen executable base path: {base_path}")
            else:
                # Development mode - use YANGO root directory (parent of modules)
                current_file = os.path.abspath(__file__)
                modules_dir = os.path.dirname(current_file)
                base_path = os.path.dirname(modules_dir)  # Go up one level from modules to YANGO
                print(f"🔧 Using development mode base path: {base_path}")
    except Exception as e:
        # Fallback to YANGO root directory
        current_file = os.path.abspath(__file__)
        modules_dir = os.path.dirname(current_file)
        base_path = os.path.dirname(modules_dir)  # Go up one level from modules to YANGO
        print(f"🔧 Fallback to YANGO root directory: {base_path} (Error: {e})")

    full_path = os.path.join(base_path, relative_path)
    print(f"🔍 Resource path for '{relative_path}': {full_path}")
    return full_path


def get_image_path(image_name):
    """
    Get path to image file with multiple fallback options

    Args:
        image_name (str): Name of the image file (e.g., "YANGO.png")

    Returns:
        list: List of possible paths to try
    """
    paths = [
        get_resource_path(f"images/{image_name}"),
        get_resource_path(image_name),
        f"images/{image_name}",  # Fallback for development
        image_name
    ]
    return paths


def get_osn_image_path(image_name):
    """
    Get path to OSN-specific image file with multiple fallback options

    Args:
        image_name (str): Name of the image file (e.g., "OSN+.png")

    Returns:
        list: List of possible paths to try
    """
    paths = [
        get_resource_path(f"images-osn/images/{image_name}"),  # OSN specific images/images folder
        get_resource_path(f"images-osn/{image_name}"),        # OSN specific images folder
        get_resource_path(f"images/{image_name}"),            # Fallback to regular images
        get_resource_path(image_name),
        f"images-osn/images/{image_name}",  # Fallback for development
        f"images-osn/{image_name}",         # Fallback for development
        f"images/{image_name}",             # Fallback for development
        image_name
    ]
    return paths


def load_image_with_fallback(image_name, fallback_text="", fallback_style=""):
    """
    Load image with fallback options

    Args:
        image_name (str): Name of the image file
        fallback_text (str): Text to use if image fails to load
        fallback_style (str): Style to apply to fallback text

    Returns:
        tuple: (success, pixmap_or_text, is_pixmap)
    """
    from PySide6.QtGui import QPixmap

    paths = get_image_path(image_name)

    for path in paths:
        try:
            pixmap = QPixmap(path)
            if not pixmap.isNull():
                print(f"✅ Image loaded successfully from: {path}")
                return True, pixmap, True
        except Exception as e:
            print(f"❌ Failed to load image from {path}: {str(e)}")
            continue

    print(f"❌ All image paths failed for {image_name}, using fallback")
    return False, fallback_text, False


def load_osn_image_with_fallback(image_name, fallback_text="", fallback_style=""):
    """
    Load OSN-specific image with fallback options

    Args:
        image_name (str): Name of the image file
        fallback_text (str): Text to use if image fails to load
        fallback_style (str): Style to apply to fallback text

    Returns:
        tuple: (success, pixmap_or_text, is_pixmap)
    """
    from PySide6.QtGui import QPixmap

    paths = get_osn_image_path(image_name)

    for path in paths:
        try:
            pixmap = QPixmap(path)
            if not pixmap.isNull():
                print(f"✅ OSN image loaded successfully from: {path}")
                return True, pixmap, True
        except Exception as e:
            print(f"❌ Failed to load OSN image from {path}: {str(e)}")
            continue

    print(f"❌ All OSN image paths failed for {image_name}, using fallback")
    return False, fallback_text, False


def get_shahid_image_path(image_name):
    """
    Get path to Shahid-specific image file with multiple fallback options

    Args:
        image_name (str): Name of the image file (e.g., "Shahid_logo_light.png")

    Returns:
        list: List of possible paths to try
    """
    paths = [
        get_resource_path(f"images-shahid/images/{image_name}"),  # Shahid specific images/images folder
        get_resource_path(f"images-shahid/{image_name}"),        # Shahid specific images folder
        get_resource_path(f"images/{image_name}"),               # Fallback to regular images
        get_resource_path(image_name),
        f"images-shahid/images/{image_name}",  # Fallback for development
        f"images-shahid/{image_name}",         # Fallback for development
        f"images/{image_name}",                # Fallback for development
        image_name
    ]
    return paths


def load_shahid_image_with_fallback(image_name, fallback_text="", fallback_style=""):
    """
    Load Shahid-specific image with fallback options

    Args:
        image_name (str): Name of the image file
        fallback_text (str): Text to use if image fails to load
        fallback_style (str): Style to apply to fallback text

    Returns:
        tuple: (success, pixmap_or_text, is_pixmap)
    """
    from PySide6.QtGui import QPixmap

    paths = get_shahid_image_path(image_name)

    for path in paths:
        try:
            pixmap = QPixmap(path)
            if not pixmap.isNull():
                print(f"✅ Shahid image loaded successfully from: {path}")
                return True, pixmap, True
        except Exception as e:
            print(f"❌ Failed to load Shahid image from {path}: {str(e)}")
            continue

    print(f"❌ All Shahid image paths failed for {image_name}, using fallback")
    return False, fallback_text, False


def get_binary_path(binary_name):
    """
    Get path to binary file
    
    Args:
        binary_name (str): Name of the binary file
        
    Returns:
        str: Path to the binary
    """
    return get_resource_path(f"binaries/{binary_name}")


def get_config_path(config_name):
    """
    Get path to config file
    
    Args:
        config_name (str): Name of the config file
        
    Returns:
        str: Path to the config file
    """
    return get_resource_path(f"config/{config_name}")


def get_theme_path(theme_name):
    """
    Get path to theme file
    
    Args:
        theme_name (str): Name of the theme file
        
    Returns:
        str: Path to the theme file
    """
    return get_resource_path(f"themes/{theme_name}")


def ensure_directory_exists(path):
    """
    Ensure directory exists, create if it doesn't
    
    Args:
        path (str): Directory path
    """
    try:
        Path(path).mkdir(parents=True, exist_ok=True)
        print(f"✅ Directory ensured: {path}")
    except Exception as e:
        print(f"❌ Failed to create directory {path}: {str(e)}")


def get_base_directory():
    """
    Get the base directory of the application

    Returns:
        str: Base directory path
    """
    try:
        # Check if running as Nuitka onefile
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller mode
            return sys._MEIPASS
        elif '__compiled__' in globals():
            # Nuitka compiled mode
            return os.path.dirname(sys.executable)
        elif getattr(sys, 'frozen', False):
            # Frozen executable (Nuitka or PyInstaller)
            return os.path.dirname(sys.executable)
        else:
            # Development mode - use YANGO root directory (parent of modules)
            current_file = os.path.abspath(__file__)
            modules_dir = os.path.dirname(current_file)
            return os.path.dirname(modules_dir)  # Go up one level from modules to YANGO
    except Exception:
        # Development mode fallback - use YANGO root directory
        current_file = os.path.abspath(__file__)
        modules_dir = os.path.dirname(current_file)
        return os.path.dirname(modules_dir)


def is_executable_mode():
    """
    Check if running in executable mode (PyInstaller/Nuitka)

    Returns:
        bool: True if running as executable
    """
    return (hasattr(sys, '_MEIPASS') or
            '__compiled__' in globals() or
            getattr(sys, 'frozen', False))


def print_resource_info():
    """Print resource information for debugging"""
    print("🔍 Resource Information:")
    print(f"   Executable mode: {is_executable_mode()}")
    print(f"   Base directory: {get_base_directory()}")
    print(f"   Current working directory: {os.getcwd()}")
    
    # Test some common paths
    test_paths = [
        "images/YANGO.png",
        "images/YANGOTO.png",
        "binaries/N_m3u8DL-RE.exe",
        "config/recent_urls.json"
    ]
    
    for path in test_paths:
        full_path = get_resource_path(path)
        exists = os.path.exists(full_path)
        print(f"   {path}: {'✅' if exists else '❌'} {full_path}")
