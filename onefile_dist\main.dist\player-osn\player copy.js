/**
 * Video Player MPD/M3U8/M3U/EPG (Async Key Fetching)
 * v
 * <AUTHOR> (Modified with async key fetching)
 * @license Video Player MPD/M3U8/M3U/EPG © 2023 by Sharkiller is licensed under CC BY-NC-ND 4.0.v
 * To view a copy of this license, visit https://creativecommons.org/licenses/by-nc-nd/4.0/
 *
 * Modified for standalone use with button activation from HTML and async key fetching.
 */

// Define global shaka/hls data holders if they don't exist
var shakadata = window.shakadata || {};
var hlsdata = window.hlsdata || {};

!function(){ // Start main IIFE scope

    // --- Original Settings Object (defaults used as Chrome Storage is unavailable) ---
    let s={
        autoLowLatency:!0,
        audioLang:"ar", // <--- تم التعديل هنا
        compressorEnabled:!1,
        customWV:!1,
        customWV_data:"",
        customCK:!1,
        customCK_data:{},
        extra:{clearkeys:!1}
    };
    let r=""; // Global variable for the stream URL, potentially used by helper functions
    window.urlCk=!1; // Global variable for ClearKey URL, used by getLicense and potentially l_async fallback

    // --- Original Helper Functions (o, n, c, i) ---
    // These are synchronous parsing functions and should be fine.
    function o(e){e=e.replaceAll("-","+").replaceAll("_","/");let t=atob(e),a=(t=t.split(""),"");return t.forEach(e=>{a+=e.codePointAt(0).toString(16).padStart(2,"0")}),a}
    function n(e){if(!e || typeof e !== 'string') return false; if(/[ {"']*([0-9a-f]{32})[ "']*:[ "']*([0-9a-f]{32})[ }"']*/i.test(e)){var t={};for(const a of e.matchAll(/[ {"']*([0-9a-f]{32})[ "']*:[ "']*([0-9a-f]{32})[ }"']*/gi))t[a[1]]=a[2];return t}return!1}
    function c(e){if(!e || typeof e !== 'string') return false; let a={};try{a=JSON.parse(e)}catch(e){return!1}if(Array.isArray(a?.keys)){let t={};return a.keys.forEach(e=>{try{t[o(e.kid)]=o(e.k)}catch(parseErr){console.error("Error parsing base64 in key object:", e, parseErr)}}),t}return!1}
    function i(e){ // Processes key data string (JSON or key:kid pairs) into DRM config
        var t,a=n(e); // Try parsing as key:kid pairs first
        if(!a){ a=c(e); } // Try parsing as JSON if key:kid failed
        if(a && Object.keys(a).length > 0){ // If parsing succeeded and we have keys
             t=Object.entries(a)[0]; // Get the first key entry
             // Store the full clearKeys map for Shaka configuration later
             // Store the full clearKeys map for Shaka configuration later
             s.extra.clearkeys={drm:{clearKeys:a}};
             console.log("Parsed ClearKeys for Shaka:", JSON.stringify(a));
             // Return true indicating success, keys are stored in s.extra.clearkeys
             return true;
        }
        console.warn("Failed to parse key data string using n() or c().");
        s.extra.clearkeys = false; // Ensure old keys are cleared if parsing fails
        return false; // Indicate failure
    }
    // --- End Original Helper Functions ---

     // --- NEW Async function to fetch key data from URL ---
     function fetchKeyDataAsync(url) {
        console.log("Attempting to fetch key data asynchronously from:", url);
        return new Promise((resolve) => { // Resolve with false on failure, no reject needed here
            var t = new XMLHttpRequest();
            t.open("GET", url, true); // ASYNCHRONOUS request
            t.timeout = 15000; // Set timeout (15 seconds)

            t.onload = function () {
                if (t.status >= 200 && t.status < 300) {
                    // Basic check if response might be valid keys before resolving
                    if (n(t.responseText) !== false || c(t.responseText) !== false) {
                        console.log("Successfully fetched key data from URL.");
                        resolve(t.responseText); // Resolve with the fetched text
                    } else {
                         console.warn("Fetched key data from URL, but failed basic parsing check (n/c). Content:", t.responseText.substring(0, 100));
                         resolve(false); // Resolve with false if parsing check fails
                    }
                } else {
                    console.warn(`Key data request failed with status: ${t.status} for URL: ${url}`);
                    resolve(false); // Resolve with false on non-2xx status
                }
            };
            t.onerror = function () {
                console.error(`Network error during key data request for URL: ${url}`);
                resolve(false); // Resolve with false on network error
            };
             t.ontimeout = function () {
                 console.error(`Key data request timed out for URL: ${url}`);
                 resolve(false); // Resolve with false on timeout
             };

            try {
                 t.send();
            } catch (sendError) {
                 console.error(`Error sending key data request for URL: ${url}`, sendError);
                 resolve(false);
            }
        });
    }

    // --- REVISED l function (now l_async) ---
    // Tries to process keyData: checks if it's a URL to fetch async, otherwise processes directly.
    // Returns true if keys were processed successfully (and stored in s.extra.clearkeys), false otherwise.
    async function l_async(keyDataInput, streamUrl) {
        let keyDataString;
        const trimmedKeyData = typeof keyDataInput === 'string' ? keyDataInput.trim() : '';

        if (!trimmedKeyData) {
            console.log("No key data provided.");
            s.extra.clearkeys = false; // Clear any old keys
            return false;
        }

        // Check if the input is likely a URL
        if (/^https?:\/\//.test(trimmedKeyData)) {
            const keyUrl = trimmedKeyData;
            const fetchedData = await fetchKeyDataAsync(keyUrl); // Await the async fetch

            if (fetchedData === false) {
                 console.warn("Failed to fetch or validate key data from URL:", keyUrl);
                 // Original fallback logic for HLS when sync XHR failed: set urlCk
                 // Mimic this if async fetch fails for an HLS stream
                 if (/m3u8/i.test(streamUrl)) {
                     console.log("Setting window.urlCk for HLS fallback:", keyUrl);
                     window.urlCk = keyUrl; // Used by getLicense
                     // Return placeholder? Original code returned {clearkey:{keyId:"",key:""}} here.
                     // Let's return false for consistency, player might handle HLS key URI directly? Needs testing.
                     s.extra.clearkeys = false;
                     return false; // Let's indicate failure consistently
                 }
                 s.extra.clearkeys = false;
                 return false; // Indicate failure for non-HLS or if HLS fallback doesn't apply
            }
            keyDataString = fetchedData; // Use the fetched data string
        } else {
            // If not a URL, assume it's the key data itself (JSON or key:kid string)
            keyDataString = trimmedKeyData;
        }

        // Process the resulting key data string (either direct input or fetched) using i()
        return i(keyDataString);
    }
    // --- End REVISED l function ---


    // --- Original getLicense function (for URL-based keys, potentially used by HLS fallback) ---
    // This remains synchronous as it might be called directly by the player in some DRM flows.
    window.getLicense=e=>{
         if (!window.urlCk) {
             console.error("getLicense called but window.urlCk is not set.");
             return false;
         }
         console.log("getLicense called for URL:", window.urlCk);
         var t=new XMLHttpRequest;
         t.open("POST",window.urlCk,!1); // Synchronous! Keep as is for potential player integrations.
         try{
             t.send(e);
             console.log("getLicense Sync XHR status:", t.status);
         } catch(err){
             console.error("Error sending sync getLicense request:", err);
             return!1;
         }
         return 200===t.status&&t.responseText
    };

    // --- Event Listeners (hlsdataReady, shakadataReady) ---
    // These should remain largely the same, configuring the player instance *after* it's created.
    document.addEventListener("hlsdataReady",()=>{
        console.log("hlsdataReady event triggered.");
        // Configure HLS.js instance if needed (e.g., widevine)
        // Might need adjustments if widevine license URL comes from async fetch
    });

    document.addEventListener("shakadataReady",()=>{
        console.log("shakadataReady event triggered.");
        if (typeof window.shakadata === 'undefined' || !window.shakadata.shakaPlayer) {
            console.warn("shakadataReady: window.shakadata.shakaPlayer not found.");
            return;
        }
        let shakaPlayerInstance = window.shakadata.shakaPlayer;
        console.log("Shaka Player Version:", shaka.Player.version);

        // Basic configuration (already applied before?)
        try {
            // Re-apply basic config in case instance was recreated
            shakaPlayerInstance.configure({
                preferredAudioLanguage:s.audioLang,
                preferredTextLanguage:s.audioLang,
                streaming:{autoLowLatencyMode:s.autoLowLatency}
            });
             console.log("shakadataReady: Applied basic config.");
        } catch(e) {
             console.error("shakadataReady: Error applying basic config to Shaka:", e);
        }

        // Apply ClearKey config if available from the last call to l_async() -> i()
        // This relies on s.extra.clearkeys being set *before* player.setup() is called.
        if(s.extra.clearkeys && s.extra.clearkeys.drm && s.extra.clearkeys.drm.clearKeys){
            console.log("shakadataReady: Applying ClearKeys:", JSON.stringify(s.extra.clearkeys.drm.clearKeys));
            try {
                shakaPlayerInstance.configure({ drm: { clearKeys: s.extra.clearkeys.drm.clearKeys } });
                console.log("shakadataReady: Shaka ClearKeys config applied successfully.");
            } catch (configError) {
                console.error("shakadataReady: Error applying ClearKeys config to Shaka:", configError);
            }
        } else {
             console.log("shakadataReady: No ClearKeys found in s.extra to apply.");
             // Optionally clear existing config if needed (already done in previous version)
            try {
                 let currentDrmConfig = shakaPlayerInstance.getConfiguration().drm;
                 if (currentDrmConfig && currentDrmConfig.clearKeys && Object.keys(currentDrmConfig.clearKeys).length > 0) {
                     console.log("shakadataReady: Clearing previous Shaka ClearKeys config.");
                     shakaPlayerInstance.configure({ drm: { clearKeys: {} } });
                 }
             } catch(e) {
                 console.error("shakadataReady: Error checking/clearing Shaka clearKeys config:", e);
             }
        }
    });
    // --- End Original Event Listeners ---


    // --- Original Compressor Functions (d, m) ---
    // Kept as is, assuming they work independently.
    function d(e){var videoEl=document.querySelector("video");if(!videoEl) return; if(!0===e){let audioCtx,sourceNode,compressorNode,gainNode;gainNode=videoEl._extension_source&&videoEl._extension_compressor&&videoEl._extension_gain?(audioCtx=videoEl._extension_source.context,sourceNode=videoEl._extension_source,compressorNode=videoEl._extension_compressor,videoEl._extension_gain):(audioCtx=new (window.AudioContext || window.webkitAudioContext)(),sourceNode=audioCtx.createMediaElementSource(videoEl),compressorNode=audioCtx.createDynamicsCompressor(),new window.GainNode(audioCtx,{gain:2}));compressorNode.threshold.value=-50,compressorNode.knee.value=40,compressorNode.ratio.value=12,compressorNode.attack.value=0,compressorNode.release.value=.25,sourceNode.connect(compressorNode),compressorNode.connect(gainNode),gainNode.connect(audioCtx.destination),videoEl._extension_source=sourceNode,videoEl._extension_compressor=compressorNode,videoEl._extension_gain=gainNode}else videoEl._extension_source&&(videoEl._extension_compressor.disconnect(),videoEl._extension_gain.disconnect(),(e=videoEl._extension_source).disconnect(),e.connect(e.context.destination))}
    function m(e){var t=document.createElement("template"),a=(t.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon btn-compressor-off" viewBox="0 0 640 512"><path d="M352 32c0-17.7-14.3-32-32-32s-32 14.3-32 32V480c0 17.7 14.3 32 32 32s32-14.3 32-32V32zM544 96c0-17.7-14.3-32-32-32s-32 14.3-32 32V416c0 17.7 14.3 32 32 32s32-14.3 32-32V96zM256 128c0-17.7-14.3-32-32-32s-32 14.3-32 32V384c0 17.7 14.3 32 32 32s32-14.3 32-32V128zm192 32c0-17.7-14.3-32-32-32s-32 14.3-32 32V352c0 17.7 14.3 32 32 32s32-14.3 32-32V160zM160 224c0-17.7-14.3-32-32-32s-32 14.3-32 32v64c0 17.7 14.3 32 32 32s32-14.3 32-32V224zM0 256a32 32 0 1 0 64 0A32 32 0 1 0 0 256zm576 0a32 32 0 1 0 64 0 32 32 0 1 0 -64 0z"/></svg>',document.createElement("template")),ev=(a.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon btn-compressor-on" viewBox="0 0 640 512"><path d="M320 0c12 0 22.1 8.8 23.8 20.7l42 304.4L424.3 84.2c1.9-11.7 12-20.3 23.9-20.2s21.9 8.9 23.6 20.6l28.2 197.3 20.5-102.6c2.2-10.8 11.3-18.7 22.3-19.3s20.9 6.4 24.2 16.9L593.7 264H616c13.3 0 24 10.7 24 24s-10.7 24-24 24H576c-10.5 0-19.8-6.9-22.9-16.9l-4.1-13.4-29.4 147c-2.3 11.5-12.5 19.6-24.2 19.3s-21.4-9-23.1-20.6L446.7 248.3l-39 243.5c-1.9 11.7-12.1 20.3-24 20.2s-21.9-8.9-23.5-20.7L320 199.6 279.8 491.3c-1.6 11.8-11.6 20.6-23.5 20.7s-22.1-8.5-24-20.2l-39-243.5L167.8 427.4c-1.7 11.6-11.4 20.3-23.1 20.6s-21.9-7.8-24.2-19.3l-29.4-147-4.1 13.4C83.8 305.1 74.5 312 64 312H24c-13.3 0-24-10.7-24-24s10.7-24 24-24H46.3l26.8-87.1c3.2-10.5 13.2-17.5 24.2-16.9s20.2 8.5 22.3 19.3l20.5 102.6L168.2 84.6c1.7-11.7 11.7-20.5 23.6-20.6s22 8.5 23.9 20.2l38.5 240.9 42-304.4C297.9 8.8 308 0 320 0z"/></svg>',e.currentTarget);
        if(ev.querySelector("svg").classList.contains("btn-compressor-off")){
            ev.querySelector("svg").replaceWith(a.content.firstChild);
            ev.querySelector(".jw-text").innerText="Compressor Off"; // Static text
            d(!0);
        } else {
            ev.querySelector("svg").replaceWith(t.content.firstChild);
            ev.querySelector(".jw-text").innerText="Compressor On"; // Static text
            d(!1);
        }
    }
    // --- End Original Compressor Functions ---

    // --- REVISED Initialization Logic ---
    // Now an ASYNC function because it calls l_async
    async function initializePlayer(streamUrl, keyData) {
        console.log("Async initializePlayer called.");
        r = streamUrl; // Update global 'r'

        // --- 1. Process Key Data Asynchronously ---
        s.extra.clearkeys = false; // Reset clear keys state
        let keysProcessedSuccessfully = false; // Flag to track if keys were parsed
        if (keyData) {
            console.log("Processing key data...");
            try {
                 keysProcessedSuccessfully = await l_async(keyData, streamUrl); // Await the async key processing
                 if (keysProcessedSuccessfully) {
                      console.log("l_async indicates keys were processed and stored in s.extra.clearkeys.");
                 } else {
                      console.warn("l_async indicates key processing failed or no keys found.");
                      // s.extra.clearkeys should be false from l_async/i failure path
                 }
            } catch (error) {
                 console.error("Error during async key processing (l_async):", error);
                 s.extra.clearkeys = false; // Ensure reset on error
                 keysProcessedSuccessfully = false;
                 // Optionally alert the user or display an error message here
                 // alert("خطأ في معالجة مفتاح التشفير.");
            }
        } else {
             console.log("No key data provided to initializePlayer.");
             s.extra.clearkeys = false; // Ensure reset
        }

        // --- 2. Prepare Player Configuration ---
        let playerConfig = {
            playlist: [{ sources: [{ file: streamUrl, default: true }] }],
            width: "100%",
            height: "100%",
            stretching: "uniform",
            aspectratio: "16:9",
            autostart: true,
            preload: "auto",
            liveSyncDuration: 5,
            liveTimeout: 0,
            playbackRateControls: true,
            // drm: drmConfig, // REMOVED - Rely on Shaka provider via shakadataReady event
            defaultTrack: 1,
            abouttext: "Stream Player",
        };

        // Load skin dynamically
        const playerDiv = document.getElementById('player');
        if (playerDiv && playerDiv.dataset.skinUrl) {
            playerConfig.skin = { name: "custom", url: playerDiv.dataset.skinUrl };
            if (!document.querySelector(`link[href="${playerDiv.dataset.skinUrl}"]`)) {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = playerDiv.dataset.skinUrl;
                document.head.appendChild(link);
                 console.log("Dynamically loaded skin CSS:", playerDiv.dataset.skinUrl);
            }
        } else {
             console.warn("Player skin URL not found in data-skin-url attribute.");
        }

        // Determine stream type
        if (streamUrl.toLowerCase().includes('.m3u8')) {
            playerConfig.playlist[0].sources[0].type = 'hls';
        } else if (streamUrl.toLowerCase().includes('.mpd')) {
            playerConfig.playlist[0].sources[0].type = 'dash';
        }

        // Apply DRM config if successfully processed - REMOVED this block
        // if (drmConfig) {
        //     console.log("Applying processed DRM config to playlist:", JSON.stringify(drmConfig));
        //     playerConfig.playlist[0].sources[0].drm = drmConfig; // REMOVED
        // }

         // --- 3. Trigger shakadataReady BEFORE setting up player instance ---
         // This allows Shaka provider to pick up the s.extra.clearkeys if it exists (set during l_async -> i).
         // This timing seems crucial based on how provider.shaka.js might work.
         if (playerConfig.playlist[0].sources[0].type === 'dash' && typeof window.shakadata !== 'undefined' && window.shakadata.shakaPlayer) {
             console.log("Manually triggering shakadataReady before setup for potential DRM config.");
             document.dispatchEvent(new CustomEvent('shakadataReady'));
         } else if (playerConfig.playlist[0].sources[0].type === 'hls' && typeof window.hlsdata !== 'undefined' /*&& check if hls needs similar trigger */) {
              // Trigger hlsdataReady if needed for HLS DRM setup? Review hlsdata logic.
              // console.log("Manually triggering hlsdataReady before setup?");
              // document.dispatchEvent(new CustomEvent('hlsdataReady'));
         }


        console.log("Final JW Player Config before setup:", JSON.stringify(playerConfig, null, 2));

        // --- 4. Setup the Player Instance ---
        let playerInstance;
        try {
             playerInstance = jwplayer("player"); // Get div
             if (playerInstance && typeof playerInstance.remove === 'function' && playerInstance.getState() !== 'idle') {
                 console.log("Attempting to remove previous player instance.");
                 await playerInstance.remove(); // Use await if remove() is async (check docs)
                 console.log("Previous player instance removed.");
                 playerInstance = jwplayer("player"); // Re-acquire after removal
             }
        } catch (e) {
            console.error("Error getting or removing player instance:", e);
            // Handle error - maybe display message in player div
            const playerElement = document.getElementById('player');
            if (playerElement) {
                playerElement.innerHTML = `<div style="color: red; padding: 20px; text-align: center;">Error initializing player container.</div>`;
            }
            return; // Stop initialization
        }

        // Setup the player
        try {
            playerInstance.setup(playerConfig).on("ready",()=>{
                console.log("Player Ready!");
                // Add compressor button (only if not already added)
                if (!document.querySelector(".jw-icon.btn-compressor")) {
                    try {
                         playerInstance.addButton(
                              '<svg xmlns="http://www.w3.org/2000/svg" class="jw-svg-icon btn-compressor-off" viewBox="0 0 640 512"><path d="M352 32c0-17.7-14.3-32-32-32s-32 14.3-32 32V480c0 17.7 14.3 32 32 32s32-14.3 32-32V32zM544 96c0-17.7-14.3-32-32-32s-32 14.3-32 32V416c0 17.7 14.3 32 32 32s32-14.3 32-32V96zM256 128c0-17.7-14.3-32-32-32s-32 14.3-32 32V384c0 17.7 14.3 32 32 32s32-14.3 32-32V128zm192 32c0-17.7-14.3-32-32-32s-32 14.3-32 32V352c0 17.7 14.3 32 32 32s32-14.3 32-32V160zM160 224c0-17.7-14.3-32-32-32s-32 14.3-32 32v64c0 17.7 14.3 32 32 32s32-14.3 32-32V224zM0 256a32 32 0 1 0 64 0A32 32 0 1 0 0 256zm576 0a32 32 0 1 0 64 0 32 32 0 1 0 -64 0z"/></svg>',
                              "Compressor On", m, "btn-compressor", "btn-compressor"
                         );
                         let volumeBtn = document.querySelector(".jw-icon.jw-icon-volume");
                         let compressorBtn = document.querySelector(".jw-icon.btn-compressor");
                          setTimeout(() => {
                             if (volumeBtn && compressorBtn && volumeBtn.parentElement && compressorBtn.parentElement) {
                                 volumeBtn.parentElement.after(compressorBtn.parentElement);
                             }
                             if(s.compressorEnabled && compressorBtn && compressorBtn.querySelector('.btn-compressor-off')){
                                  compressorBtn.click();
                             }
                         }, 150); // Increased timeout slightly
                    } catch (e) { console.error("Error adding compressor button:", e); }
                }
            }).on("error",e=>{
                console.error("JW Player Error Event:", e);
                const playerElement = document.getElementById('player');
                if (playerElement) {
                     playerElement.innerHTML = `<div style="color: red; padding: 20px; text-align: center;">Player Error: ${e.message} (Code: ${e.code})<br>Please check stream details and keys.</div>`;
                }
                 // Allow button to be shown again on error - handled by DOMContentLoaded listener
            }).on("levels",r=>{ // Quality level display logic (original, adapted for safety)
                 // ... (keep original levels logic as it was) ...
                 try {
                     let playerElement = document.getElementById('player');
                     let qualityMenu = playerElement ? playerElement.querySelector(".jw-settings-submenu-items[role='menu'][aria-label='Quality']") : null;
                     if (!qualityMenu) return;
                     qualityMenu.querySelectorAll("button[role='menuitemradio']").forEach((buttonEl, index) => {
                         if (index !== 0) {
                             const levelIndex = index -1;
                             if (!r || !r.levels || !r.levels[levelIndex]) { return; }
                             let levelData = r.levels[levelIndex];
                             let frameRateInfo = "";
                             var providerData;
                             let currentProvider = playerInstance.getProvider();
                             if (currentProvider && currentProvider.name === "shaka" && typeof window.shakadata !== 'undefined' && window.shakadata.shakaPlayer) {
                                  try { let trackId = levelData.shakaId; if (trackId !== undefined) { providerData = window.shakadata.shakaPlayer.getVariantTracks().find(track => track.id === trackId); if (providerData?.frameRate) { frameRateInfo = " " + parseFloat(Number(providerData.frameRate).toFixed(2)) + " fps"; } } } catch (shakaError) { console.warn("Error getting Shaka track info:", shakaError); }
                             } else if (currentProvider && currentProvider.name === "hlsjs" && typeof window.hlsdata !== 'undefined' && window.hlsdata.levelController) {
                                  try { let hlsIndex = levelData.hlsjsIndex; if (hlsIndex !== undefined) { providerData = window.hlsdata.levelController.levels.at(hlsIndex); if (providerData?.attrs["FRAME-RATE"]) { frameRateInfo = " " + parseFloat(Number(providerData.attrs["FRAME-RATE"]).toFixed(2)) + " fps"; } } } catch (hlsError) { console.warn("Error getting HLS level info:", hlsError); }
                             }
                             let height = levelData.height || '?';
                             let bitrate = levelData.bitrate ? Math.floor(levelData.bitrate / 1e3) : '?';
                             buttonEl.innerHTML = height + "p (" + bitrate + " kbps" + frameRateInfo + ")";
                         }
                     });
                 } catch (levelsError) { console.error("Error updating quality levels display:", levelsError); }
            });

            // Return the instance if needed elsewhere
            return playerInstance;

        } catch (setupError) {
             console.error("Fatal error during playerInstance.setup():", setupError);
             const playerElement = document.getElementById('player');
             if (playerElement) {
                  playerElement.innerHTML = `<div style="color: red; padding: 20px; text-align: center;">Fatal Player Setup Error: ${setupError.message}<br>Check browser console for more details.</div>`;
             }
             // Ensure UI state (button) is reset if setup fails completely
              const playerSection = document.querySelector('.player-section');
              if (playerSection) playerSection.classList.remove('player-active', 'player-loading');
              const playButton = document.getElementById('playStreamButton');
              if (playButton) playButton.disabled = false; // Re-enable button if setup failed
             throw setupError; // Re-throw error to be caught by the click handler
        }
    } // --- End initializePlayer ---

    // --- REVISED DOMContentLoaded listener ---
    document.addEventListener('DOMContentLoaded', () => {
        const playButton = document.getElementById('playStreamButton');
        const playerSection = document.querySelector('.player-section'); // Needed for adding/removing classes

        if (!playerSection) {
             console.error("Critical: Player section container (.player-section) not found!");
             return;
        }

        if (playButton) {
            console.log("Play button (playStreamButton) found.");

            // Check if button should be initially hidden (because it's disabled due to missing data)
            if(playButton.disabled) {
                const buttonContainer = playerSection.querySelector('.play-button-container');
                if (buttonContainer) buttonContainer.style.display = 'none';
                console.log("Play button is disabled (missing data), hiding overlay.");
            } else {
                 // Add click listener only if the button is enabled
                 playButton.addEventListener('click', async () => { // Make listener async to await initializePlayer
                     console.log("Play button clicked.");
                     const streamUrl = playButton.dataset.mpdUrl;
                     const keyData = playButton.dataset.keyData;

                     if (!streamUrl) {
                         alert('خطأ: رابط البث مفقود في بيانات الزر.');
                         console.error("Stream URL missing in playStreamButton data-mpd-url attribute.");
                         return;
                     }

                     // --- UI Feedback Start ---
                     playButton.disabled = true; // Disable button during loading
                     playerSection.classList.add('player-loading'); // Add loading class (optional: style .player-loading .play-button-container with a spinner)
                     playerSection.classList.remove('player-error'); // Clear previous error state
                     console.log("UI feedback: Button disabled, loading class added.");
                     // ---

                     try {
                         // Call the async initializer function and wait for it
                         const playerInstance = await initializePlayer(streamUrl, keyData);
                         console.log("initializePlayer finished successfully.");

                         // --- UI Feedback Success (Player handles visibility via events) ---
                         // If player setup is successful, the 'play' event handler below
                         // will add 'player-active' which hides the button via CSS.
                         // We just need to remove the loading state here.
                         playerSection.classList.remove('player-loading');

                         // Optional: Attach player event listeners here if needed after setup
                         if (playerInstance) {
                            attachPlayerEventListeners(playerInstance, playerSection, playButton);
                         }
                         // ---

                     } catch (error) {
                         console.error("Error occurred during initializePlayer call:", error);
                         // --- UI Feedback Error ---
                         playerSection.classList.remove('player-loading', 'player-active');
                         playerSection.classList.add('player-error'); // Optional class for error state styling
                         playButton.disabled = false; // Re-enable button on error
                         alert("حدث خطأ أثناء تهيئة المشغل. يرجى مراجعة التفاصيل أو المحاولة مرة أخرى.");
                         // ---
                     }
                 }); // End click listener
                 console.log("Click listener added to enabled playStreamButton.");
            } // End else (button enabled)

        } else {
            console.warn("Play button (playStreamButton) not found in the DOM on load.");
        }

        console.log("Player script loaded and DOM ready.");

    }); // --- End DOMContentLoaded ---


    // --- NEW Function to attach player event listeners ---
    // This helps keep the DOMContentLoaded cleaner
    function attachPlayerEventListeners(playerInstance, playerSection, playButton) {
         console.log("Attaching player event listeners for UI state.");

         // Make sure playerSection and playButton are valid DOM elements
         if (!playerSection || !playButton) {
             console.error("Cannot attach listeners: playerSection or playButton is invalid.");
             return;
         }

         // Function to reset UI (show button)
         const showPlayButton = () => {
              playerSection.classList.remove('player-active', 'player-loading', 'player-error');
              playButton.disabled = false;
              console.log("Player event: Resetting UI, showing play button.");
         };

         // Function to hide UI (hide button)
         const hidePlayButton = () => {
              playerSection.classList.remove('player-loading', 'player-error');
              playerSection.classList.add('player-active'); // This class hides the button via CSS
              playButton.disabled = true; // Keep disabled while active
              console.log("Player event: Hiding play button (adding player-active class).");
         };

         // Attach Listeners
         playerInstance.on('play', hidePlayButton);
         playerInstance.on('buffer', hidePlayButton); // Also hide during buffering after initial play
         playerInstance.on('error', showPlayButton);
         playerInstance.on('complete', showPlayButton);
         playerInstance.on('remove', showPlayButton); // When player is removed

         // Optional: Handle pause? Sometimes users might want the button back on pause.
         // playerInstance.on('pause', showPlayButton);
    }

}(); // End main IIFE
