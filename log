QLayout: Attempting to add QLayout "" to QWidget "widgets", which already has a layout
QLayout: Attempting to add QLayout "" to QWidget "new_page", which already has a layout
✅ Shahid UI handler initialized
✅ Applied original Shahid theme
✅ Shahid styling applied
✅ Set default page to home
✅ Hidden original Shahid left menu
✅ Hidden original Shahid extra left box
✅ Hidden original Shahid extra top menu
✅ Hidden original Shahid title bar
✅ Hidden original Shahid window controls
✅ Hidden original Shahid bottom bar
✅ Hidden original Shahid credits
✅ Hidden original Shahid version
✅ Hidden original Shahid size grip
✅ Original Shahid sidebar and extra components hidden
✅ Removed bgApp borders
✅ Removed app margins
✅ Removed pages container borders
✅ Removed content box borders
✅ All borders and separators removed
✅ Original Shahid UI setup completed
✅ Connected btn_home
✅ Connected btn_widgets
💾 Saved OSN access token to: C:\Users\<USER>\OneDrive\Desktop\New folder (6)\YANGO\osn_refresh_token.txt
✅ Connected btn_new
✅ Connected btn_save
✅ Connected Shahid search signals
✅ Connected Shahid token signals
✅ Connected Shahid clear signals
✅ Connected Shahid content tabs signals
✅ Connected Shahid continue button
✅ Connected Shahid seasons list
✅ Connected Shahid episodes list selection
✅ Connected Shahid seasons continue button
✅ Connected Shahid add to queue button
✅ Connected Shahid download with IDM button
✅ Connected Shahid start download button
✅ Connected Shahid clear completed button
✅ Connected Shahid clear all button
✅ Connected Shahid select all button
✅ Connected Shahid select none button
✅ Connected Shahid recent combo selection
✅ Connected Shahid main play button
✅ Connected Shahid seasons play button
✅ Connected Shahid download options play button
✅ Shahid signals connected
🔍 Checking token...
🔍 Token value: eyJhbGciOiJIUzI1NiJ9.eyJjYWNoZSI6IlVzZXJfODhmMTFlYjdjOTc0NDI3MzllNWFjYzE4ZTMyMDE0YWMiLCJ1aWQiOiJhcHIxU2hhaGlkRW5RWUpQTUsyTmxCb25xVnZqejJFV3QiLCJkaWQiOiJXZWIiLCJzdWJpZCI6Ijg4ZjExZWI3Yzk3NDQyNzM5ZTVhY2MxOGUzMjAxNGFjIiwic3ViIjoic2hhaGlkLXRva2VuLWVuY29kZSIsImlzcyI6InNoYWhpZC10b2tlbi1lbmNvZGUiLCJpYXQiOjE3NDgxOTU5MTksImV4cCI6MTc3OTczMTkxOX0.JGOiKbYb50dpDsAi79fjoaf_qIr0cW6-CsTZbVoDK9s
🔍 Has shahid_ui: True
🔍 Has token_frame: True
✅ Token found and loaded successfully. Token frame hidden.
🔍 Checking token...
🔍 Token value: eyJhbGciOiJIUzI1NiJ9.eyJjYWNoZSI6IlVzZXJfODhmMTFlYjdjOTc0NDI3MzllNWFjYzE4ZTMyMDE0YWMiLCJ1aWQiOiJhcHIxU2hhaGlkRW5RWUpQTUsyTmxCb25xVnZqejJFV3QiLCJkaWQiOiJXZWIiLCJzdWJpZCI6Ijg4ZjExZWI3Yzk3NDQyNzM5ZTVhY2MxOGUzMjAxNGFjIiwic3ViIjoic2hhaGlkLXRva2VuLWVuY29kZSIsImlzcyI6InNoYWhpZC10b2tlbi1lbmNvZGUiLCJpYXQiOjE3NDgxOTU5MTksImV4cCI6MTc3OTczMTkxOX0.JGOiKbYb50dpDsAi79fjoaf_qIr0cW6-CsTZbVoDK9s
🔍 Has shahid_ui: True
🔍 Has token_frame: True
✅ Token found and loaded successfully. Token frame hidden.
📂 Loaded 1 recent searches from C:\Users\<USER>\OneDrive\Desktop\New folder (6)\YANGO\config\shahid_recent_searches.json
✅ Shahid VIP widget initialized successfully
✅ Shahid VIP original application integrated successfully
✅ Quick access buttons connected
✅ Settings buttons connected
[SETTINGS] Config directory: C:\Users\<USER>\OneDrive\Desktop\New folder (6)\YANGO\config
[SETTINGS] Settings file: C:\Users\<USER>\OneDrive\Desktop\New folder (6)\YANGO\config\shahid_settings.json
[SETTINGS] Settings file exists: 1486 bytes
[SETTINGS] Cleaned proxy section to ensure valid structure
[SETTINGS] Cleaned ui section to ensure valid structure
[SETTINGS] Cleaned download section to ensure valid structure
[SETTINGS] Cleaned idm section to ensure valid structure
[SETTINGS] Successfully loaded settings from C:\Users\<USER>\OneDrive\Desktop\New folder (6)\YANGO\config\shahid_settings.json
✅ Shahid settings manager loaded
✅ Settings save buttons ready for connection
✅ Settings management setup completed
C:\Users\<USER>\OneDrive\Desktop\New folder (6)\YANGO\main.py:956: DeprecationWarning: 'exec_' will be removed in the future. Use 'exec' instead.
  sys.exit(app.exec_())
Unknown property transform
Unknown property transform
Unknown property transform
C:\Users\<USER>\OneDrive\Desktop\New folder (6)\YANGO\main.py:929: DeprecationWarning: Function: 'QMouseEvent.globalPos() const' is marked as deprecated, please check the documentation for more information.
  self.dragPos = event.globalPos()
Mouse click: LEFT CLICK
⭐ Switched to Shahid VIP module
⭐ Quick access to Shahid VIP
✅ Shahid token loaded from: C:\Users\<USER>\OneDrive\Desktop\New folder (6)\YANGO\config\shahid_token.txt
File C:\Users\<USER>\OneDrive\Desktop\New folder (6)\YANGO\proxy.txt not found.
[API] Extracted content ID 2139311527 from URL using pattern: season-([0-9]+)-[0-9]+
[UI] Successfully extracted content ID: 2139311527 from input: https://shahid.mbc.net/en/series/Kung-Fu-Panda%3A-Legends-Of-Awesomeness-season-1/season-2139311527-2151462527
[UI] Searching for content with input: https://shahid.mbc.net/en/series/Kung-Fu-Panda%3A-Legends-Of-Awesomeness-season-1/season-2139311527-2151462527

[API] Getting content details for: https://shahid.mbc.net/en/series/Kung-Fu-Panda%3A-Legends-Of-Awesomeness-season-1/season-2139311527-2151462527
[API] Extracted content ID 2139311527 from URL using pattern: season-([0-9]+)-[0-9]+
[API] Trying to get content as SERIES with ID: 2139311527
[API] Content not found as SERIES, trying as MOVIE with ID: 2139311527
[API] Content has seasons but was returned from movie endpoint, treating as SERIES
API Response:
{
  "responseCode": 200,
  "success": true,
  "currentDate": "2025-06-18T01:54:42+0000",
  "country": "SAU",
  "productModel": {
    "id": 2139311527,
    "title": "Kung Fu Panda: Legends Of Awesomeness",
    "description": "With the help of the Furious Five, Po, a clumsy, enthusiastic and always hungry panda, protects the valley from the attacks of the villains while perfecting his kung-fu skills, to the best of his abilities…",
    "shortDescription": "",
    "image": {
      "thumbnailImage": "https://shahid.mbc.net/mediaObject/246d6010-1732-4e52-9001-e88807405b17?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "posterImage": "https://shahid.mbc.net/mediaObject/df78b39b-cf3d-44a6-8ef1-6becd10769a1?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "heroSliderImage": "https://shahid.mbc.net/mediaObject/386ea1b0-1ee3-4cbc-8a81-764619278d73?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "landscapeClean": "https://shahid.mbc.net/mediaObject/99f00b70-8239-4e02-a51d-b2cfc8452b3f?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "posterClean": "https://shahid.mbc.net/mediaObject/a58e159a-0688-488c-97a4-a4dd852867e8?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "posterHero": "https://shahid.mbc.net/mediaObject/c8fcf044-cc01-44f7-a7e8-64b2b81eb615?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "posterTop10": "",
      "posterBundle": "",
      "onboardingCategoryImage": "",
      "squareImage": ""
    },
    "thumbnailImage": "https://shahid.mbc.net/mediaObject/246d6010-1732-4e52-9001-e88807405b17?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
    "mainImage": "https://shahid.mbc.net/mediaObject/386ea1b0-1ee3-4cbc-8a81-764619278d73?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
    "createdDate": "2025-01-29T00:00:00+0000",
    "modifiedDate": "2025-02-05T11:42:35+0000",
    "productionDate": null,
    "releaseDate": null,
    "productUrl": {
      "url": "https://shahid.mbc.net/en/series/kung-fu-panda:-legends-of-awesomeness/series-2139311527",
      "shortenUrl": "https://shdl.ink/uhFnQr"
    },
    "productUrls": [
      {
        "url": "https://shahid.mbc.net/ar/series/كونغ-فو-باندا:-أساطير-الروعة/series-2139311527",
        "shortenUrl": "https://shdl.ink/owwhwu"
      },
      {
        "url": "https://shahid.mbc.net/en/series/kung-fu-panda:-legends-of-awesomeness/series-2139311527",
        "shortenUrl": "https://shdl.ink/uhFnQr"
      },
      {
        "url": "https://shahid.mbc.net/fr/series/kung-fu-panda:-legends-of-awesomeness/series-2139311527",
        "shortenUrl": "https://shdl.ink/tFUKNJ"
      }
    ],
    "dialect": {
      "id": 1197975878,
      "title": "Dubbed in Persian"
    },
    "genres": [
      {
        "id": 7935,
        "title": "Action"
      },
      {
        "id": 9654,
        "title": "Adventure"
      },
      {
        "id": 9649,
        "title": "Animation"
      }
    ],
    "microGenres": [],
    "channels": [],
    "displayMetadata": [],
    "decades": null,
    "persons": [],
    "writers": [],
    "directors": [
      {
        "id": 2165681517,
        "firstName": "Michael Mullen",
        "fullName": "Michael Mullen",
        "rank": 1
      },
      {
        "id": 2165684517,
        "firstName": "Lane Lueras",
        "fullName": "Lane Lueras",
        "rank": 2
      },
      {
        "id": 2165685517,
        "firstName": "Aaron Hammersley",
        "fullName": "Aaron Hammersley",
        "rank": 3
      }
    ],
    "coachs": [],
    "contestants": [],
    "judges": [],
    "presenters": [],
    "producers": [],
    "pricingPlans": [
      {
        "id": 30146,
        "name": null,
        "type": "BROWSE_ONLY",
        "planId": 0,
        "startDate": "2025-01-22T00:00:00+0000",
        "endDate": null,
        "offerEndDate": "2025-11-30T00:00:00+0000",
        "availability": {
          "days": 0,
          "hours": 0,
          "minutes": 0,
          "status": "NONE",
          "plus": true
        },
        "kidsAllowed": true,
        "kidsAllowedAge": 5,
        "downloadSetting": {
          "allowDownload": false,
          "name": null,
          "rental": null,
          "playback": null,
          "licenseExpiry": null
        }
      }
    ],
    "subscriptionPackages": [
      {
        "subscriptionId": "1",
        "name": "SHAHID_VIP",
        "isDefault": true
      },
      {
        "subscriptionId": "2",
        "name": "SHAHID_VIP_SPORT",
        "isDefault": false
      },
      {
        "subscriptionId": "3",
        "name": "SHAHID_VIP_SPORT_GOBX",
        "isDefault": false
      },
      {
        "subscriptionId": "4",
        "name": "SHAHID_MOBILE_ONLY",
        "isDefault": false
      },
      {
        "subscriptionId": "5",
        "name": "SHAHID_VIP_GEA",
        "isDefault": false
      },
      {
        "subscriptionId": "6",
        "name": "SHAHID_VIP_GEA_SPORT",
        "isDefault": false
      },
      {
        "subscriptionId": "7",
        "name": "SHAHID_VIP_GEA_SPORT_GOBX",
        "isDefault": false
      }
    ],
    "catalogs": [
      {
        "id": "39172",
        "name": "VIP",
        "isDefault": true
      }
    ],
    "adZone": null,
    "available": true,
    "tag": null,
    "rank": 0,
    "is4K": null,
    "productSubType": "SERIES",
    "showType": "SERIES",
    "seasons": [
      {
        "id": 2151462527,
        "title": "Kung Fu Panda: Legends Of Awesomeness",
        "seasonNumber": "1",
        "seasonName": "2",
        "numberOfAVODEpisodes": 0,
        "plus": true,
        "subscriptionPackages": [
          {
            "subscriptionId": "1",
            "name": "SHAHID_VIP",
            "isDefault": true
          },
          {
            "subscriptionId": "2",
            "name": "SHAHID_VIP_SPORT",
            "isDefault": false
          },
          {
            "subscriptionId": "3",
            "name": "SHAHID_VIP_SPORT_GOBX",
            "isDefault": false
          },
          {
            "subscriptionId": "4",
            "name": "SHAHID_MOBILE_ONLY",
            "isDefault": false
          },
          {
            "subscriptionId": "5",
            "name": "SHAHID_VIP_GEA",
            "isDefault": false
          },
          {
            "subscriptionId": "6",
            "name": "SHAHID_VIP_GEA_SPORT",
            "isDefault": false
          },
          {
            "subscriptionId": "7",
            "name": "SHAHID_VIP_GEA_SPORT_GOBX",
            "isDefault": false
          }
        ],
        "catalogs": [
          {
            "id": "39172",
            "name": "VIP",
            "isDefault": true
          }
        ],
        "streamInfo": {
          "streamState": "VOD",
          "startDate": null,
          "endDate": null
        },
        "catchUp": true,
        "firstEpisodeFree": false,
        "originalAVOD": false
      }
    ],
    "numberOfAvodSeasons": 0,
    "numberOfAvodEpisodeForShow": 0,
    "showOriginallyAVOD": false,
    "avodSeasonNumber": null,
    "season": {
      "id": 2151462527,
      "title": "Kung Fu Panda: Legends Of Awesomeness",
      "description": "With the help of the Furious Five, Po, a clumsy, enthusiastic and always hungry panda, protects the valley from the attacks of the villains while perfecting his kung-fu skills, to the best of his abilities…\r\r",
      "shortDescription": "",
      "image": {
        "thumbnailImage": "https://shahid.mbc.net/mediaObject/246d6010-1732-4e52-9001-e88807405b17?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
        "posterImage": "https://shahid.mbc.net/mediaObject/df78b39b-cf3d-44a6-8ef1-6becd10769a1?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
        "heroSliderImage": "https://shahid.mbc.net/mediaObject/386ea1b0-1ee3-4cbc-8a81-764619278d73?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
        "landscapeClean": "https://shahid.mbc.net/mediaObject/99f00b70-8239-4e02-a51d-b2cfc8452b3f?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
        "posterClean": "https://shahid.mbc.net/mediaObject/a58e159a-0688-488c-97a4-a4dd852867e8?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
        "posterHero": "https://shahid.mbc.net/mediaObject/c8fcf044-cc01-44f7-a7e8-64b2b81eb615?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
        "posterTop10": "",
        "posterBundle": "",
        "onboardingCategoryImage": "",
        "squareImage": ""
      },
      "thumbnailImage": "https://shahid.mbc.net/mediaObject/246d6010-1732-4e52-9001-e88807405b17?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "mainImage": "https://shahid.mbc.net/mediaObject/386ea1b0-1ee3-4cbc-8a81-764619278d73?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "createdDate": "2025-01-29T00:00:00+0000",
      "modifiedDate": "2025-02-05T11:42:34+0000",
      "productionDate": null,
      "releaseDate": null,
      "productUrl": {
        "url": "https://shahid.mbc.net/en/series/kung-fu-panda:-legends-of-awesomeness/season-2139311527-2151462527",
        "shortenUrl": "https://shdl.ink/3foP3L"
      },
      "productUrls": [
        {
          "url": "https://shahid.mbc.net/ar/series/كونغ-فو-باندا:-أساطير-الروعة/season-2139311527-2151462527",
          "shortenUrl": "https://shdl.ink/11KOmp"
        },
        {
          "url": "https://shahid.mbc.net/fr/series/kung-fu-panda:-legends-of-awesomeness/season-2139311527-2151462527",
          "shortenUrl": "https://shdl.ink/EH1hOo"
        },
        {
          "url": "https://shahid.mbc.net/en/series/kung-fu-panda:-legends-of-awesomeness/season-2139311527-2151462527",
          "shortenUrl": "https://shdl.ink/3foP3L"
        }
      ],
      "dialect": {
        "id": 1197975878,
        "title": "Dubbed in Persian"
      },
      "genres": [
        {
          "id": 7935,
          "title": "Action"
        },
        {
          "id": 9654,
          "title": "Adventure"
        },
        {
          "id": 9649,
          "title": "Animation"
        }
      ],
      "microGenres": [],
      "channels": [],
      "displayMetadata": [],
      "decades": null,
      "persons": [],
      "writers": [],
      "directors": [
        {
          "id": 2165681517,
          "firstName": "Michael Mullen",
          "fullName": "Michael Mullen",
          "rank": 1
        },
        {
          "id": 2165684517,
          "firstName": "Lane Lueras",
          "fullName": "Lane Lueras",
          "rank": 2
        },
        {
          "id": 2165685517,
          "firstName": "Aaron Hammersley",
          "fullName": "Aaron Hammersley",
          "rank": 3
        }
      ],
      "coachs": [],
      "contestants": [],
      "judges": [],
      "presenters": [],
      "producers": [],
      "pricingPlans": [
        {
          "id": 30146,
          "name": null,
          "type": "BROWSE_ONLY",
          "planId": 0,
          "startDate": "2025-01-29T00:00:00+0000",
          "endDate": null,
          "offerEndDate": "2025-11-30T00:00:00+0000",
          "availability": {
            "days": 0,
            "hours": 0,
            "minutes": 0,
            "status": "NONE",
            "plus": true
          },
          "kidsAllowed": true,
          "kidsAllowedAge": 5,
          "downloadSetting": {
            "allowDownload": false,
            "name": null,
            "rental": null,
            "playback": null,
            "licenseExpiry": null
          }
        }
      ],
      "subscriptionPackages": [
        {
          "subscriptionId": "1",
          "name": "SHAHID_VIP",
          "isDefault": true
        },
        {
          "subscriptionId": "2",
          "name": "SHAHID_VIP_SPORT",
          "isDefault": false
        },
        {
          "subscriptionId": "3",
          "name": "SHAHID_VIP_SPORT_GOBX",
          "isDefault": false
        },
        {
          "subscriptionId": "4",
          "name": "SHAHID_MOBILE_ONLY",
          "isDefault": false
        },
        {
          "subscriptionId": "5",
          "name": "SHAHID_VIP_GEA",
          "isDefault": false
        },
        {
          "subscriptionId": "6",
          "name": "SHAHID_VIP_GEA_SPORT",
          "isDefault": false
        },
        {
          "subscriptionId": "7",
          "name": "SHAHID_VIP_GEA_SPORT_GOBX",
          "isDefault": false
        }
      ],
      "catalogs": [
        {
          "id": "39172",
          "name": "VIP",
          "isDefault": true
        }
      ],
      "adZone": null,
      "available": true,
      "tag": null,
      "rank": 0,
      "is4K": false,
      "showItem": {
        "id": 2139311527,
        "productSubType": "SERIES",
        "showType": "SERIES",
        "title": "Kung Fu Panda: Legends Of Awesomeness"
      },
      "seasonNumber": "1",
      "seasonName": "2",
      "playlists": [
        {
          "id": "S215146252749922996661693",
          "productSubType": "EPISODE",
          "type": "EPISODE",
          "title": "Episodes",
          "count": 24,
          "sortNumber": 1
        },
        {
          "id": "S215146252749922996661693",
          "productSubType": "EPISODE",
          "type": "EPISODE",
          "title": "Free Episodes",
          "count": 0,
          "sortNumber": 2,
          "playlistType": "FREE_EPISODES"
        }
      ],
      "allPlaylist": null,
      "numberOfAssets": 24,
      "numberOfclips": 0,
      "bcmSeasonID": null,
      "dialectCombined": "Dubbed in Persian",
      "genresCombined": "Action-Adventure-Animation",
      "endMarker": 0,
      "posterImage": "https://shahid.mbc.net/mediaObject/df78b39b-cf3d-44a6-8ef1-6becd10769a1?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "promoItem": null,
      "catchUp": true,
      "explicitContent": false,
      "originalContent": false,
      "productContractId": null,
      "viewStatus": "VIEW_NOW",
      "logoTitleImage": "https://shahid.mbc.net/mediaObject/d5c8afc1-15fa-434b-b453-46b5091aaade?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
      "contentOriginalLanguage": "en",
      "tracks": {
        "audios": null,
        "subtitles": null,
        "audioLabels": null
      },
      "comingSoon": {
        "comingSoonDay": "",
        "comingSoonMonth": ""
      },
      "eventType": null,
      "optaId": null,
      "numberOfEpisodes": 26,
      "numberOfAVODEpisodes": 0,
      "streamInfo": {
        "streamState": "VOD",
        "startDate": null,
        "endDate": null
      },
      "productType": "SEASON",
      "firstEpisodeFree": false
    },
    "numberOfSeasons": 1,
    "availableOnTV": true,
    "sortDate": "2025-01-29T00:00:00+0000",
    "logoTitleImage": "https://shahid.mbc.net/mediaObject/38c11325-5935-491c-9237-d8d5dace17ae?height={height}&width={width}&croppingPoint={croppingPoint}&version=1",
    "totalNumberOfEpisodes": 24,
    "eventType": null,
    "optaId": null,
    "eventSubType": null,
    "productType": "SHOW"
  }
}
[UI] Extracted title: Kung Fu Panda: Legends Of Awesomeness
🔄 Adding to recent URLs: input_text='https://shahid.mbc.net/en/series/Kung-Fu-Panda%3A-Legends-Of-Awesomeness-season-1/season-2139311527-2151462527', title='Kung Fu Panda: Legends Of Awesomeness'
🔄 add_to_recent_urls called with url='https://shahid.mbc.net/en/series/Kung-Fu-Panda%3A-Legends-Of-Awesomeness-season-1/season-2139311527-2151462527', title='Kung Fu Panda: Legends Of Awesomeness'
🔍 Extracted content_id: 'None'
⚠️ No valid content_id found, adding URL as is
🔄 Recent selection changed to: https://shahid.mbc.net/en/series/Kung-Fu-Panda%3A-Legends-Of-Awesomeness-season-1/season-2139311527-2151462527
⚠️ Could not extract valid ID from: https://shahid.mbc.net/en/series/Kung-Fu-Panda%3A-Legends-Of-Awesomeness-season-1/season-2139311527-2151462527
✅ Added URL to recent: https://shahid.mbc.net/en/series/Kung-Fu-Panda%3A-Legends-Of-Awesomeness-season-1/season-2139311527-2151462527
💾 Saved 2 recent searches to C:\Users\<USER>\OneDrive\Desktop\New folder (6)\YANGO\config\shahid_recent_searches.json
Found numberOfEpisodes in season: 26
✅ Displayed content details for: Kung Fu Panda: Legends Of Awesomeness
Attempting to load poster from URL: https://shahid.mbc.net/mediaObject/df78b39b-cf3d-44a6-8ef1-6becd10769a1?width=450&version=1&type=jpg&q=80
Poster response status code: 200
Poster loaded successfully
Tab changed to index: 1
[UI] Loading content type: SERIES
[UI] Loading seasons for series ID: 2139311527
[UI] Loading seasons for series ID: 2139311527

[INFO] Fetching series details for series_id: 2139311527
[INFO] Found 1 seasons
[INFO] Season 1 has numberOfAVODEpisodes: 0
[INFO] Found 24 episodes in playlist for season 1
[INFO] Added season: Season 1 (24 episodes), ID: 2151462527
[UI] Found 1 seasons
[UI] Season data (5+ fields): ID=2151462527, Number=1, Name=Season 1 (24 episodes), Count=24, Total=26
[UI] Adding season to list: Season 1 (24 episodes)
[UI] Selecting first season
[UI] Loading episodes for season ID: 2151462527, Count: 24, Total: 26
[UI] Updated range spinners: from 1 to 26
[UI] Fetching episodes from API for season ID: 2151462527

[INFO] Fetching season details for season_id: 2151462527
[INFO] Found numberOfEpisodes: 26
[INFO] Found episodes playlist with ID: S215146252749922996661693
[INFO] No episodes found for season 2151462527
[INFO] Found episodes playlist with ID: S215146252749922996661693
[INFO] No episodes found for season 2151462527
[INFO] Adding placeholders for missing episodes. Total: 26, Available: 0
[UI] Found 26 episodes from API
[UI] Episode data (6+ fields): ID=None, Number=1, Name=Episode 1, Title=Episode 1, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=2, Name=Episode 2, Title=Episode 2, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=3, Name=Episode 3, Title=Episode 3, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=4, Name=Episode 4, Title=Episode 4, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=5, Name=Episode 5, Title=Episode 5, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=6, Name=Episode 6, Title=Episode 6, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=7, Name=Episode 7, Title=Episode 7, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=8, Name=Episode 8, Title=Episode 8, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=9, Name=Episode 9, Title=Episode 9, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=10, Name=Episode 10, Title=Episode 10, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=11, Name=Episode 11, Title=Episode 11, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=12, Name=Episode 12, Title=Episode 12, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=13, Name=Episode 13, Title=Episode 13, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=14, Name=Episode 14, Title=Episode 14, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=15, Name=Episode 15, Title=Episode 15, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=16, Name=Episode 16, Title=Episode 16, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=17, Name=Episode 17, Title=Episode 17, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=18, Name=Episode 18, Title=Episode 18, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=19, Name=Episode 19, Title=Episode 19, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=20, Name=Episode 20, Title=Episode 20, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=21, Name=Episode 21, Title=Episode 21, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=22, Name=Episode 22, Title=Episode 22, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=23, Name=Episode 23, Title=Episode 23, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=24, Name=Episode 24, Title=Episode 24, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=25, Name=Episode 25, Title=Episode 25, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=26, Name=Episode 26, Title=Episode 26, Status=Not yet available, Available=False
[UI] Added 0 available episodes out of 0 total episodes shown
[UI] Added 0 episodes to list, continue button enabled: False
[UI] Loading episodes for season ID: 2151462527, Count: 24, Total: 26
[UI] Updated range spinners: from 1 to 26
[UI] Fetching episodes from API for season ID: 2151462527

[INFO] Fetching season details for season_id: 2151462527
[INFO] Found numberOfEpisodes: 26
[INFO] Found episodes playlist with ID: S215146252749922996661693
[INFO] No episodes found for season 2151462527
[INFO] Found episodes playlist with ID: S215146252749922996661693
[INFO] No episodes found for season 2151462527
[INFO] Adding placeholders for missing episodes. Total: 26, Available: 0
[UI] Found 26 episodes from API
[UI] Episode data (6+ fields): ID=None, Number=1, Name=Episode 1, Title=Episode 1, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=2, Name=Episode 2, Title=Episode 2, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=3, Name=Episode 3, Title=Episode 3, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=4, Name=Episode 4, Title=Episode 4, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=5, Name=Episode 5, Title=Episode 5, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=6, Name=Episode 6, Title=Episode 6, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=7, Name=Episode 7, Title=Episode 7, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=8, Name=Episode 8, Title=Episode 8, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=9, Name=Episode 9, Title=Episode 9, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=10, Name=Episode 10, Title=Episode 10, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=11, Name=Episode 11, Title=Episode 11, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=12, Name=Episode 12, Title=Episode 12, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=13, Name=Episode 13, Title=Episode 13, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=14, Name=Episode 14, Title=Episode 14, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=15, Name=Episode 15, Title=Episode 15, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=16, Name=Episode 16, Title=Episode 16, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=17, Name=Episode 17, Title=Episode 17, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=18, Name=Episode 18, Title=Episode 18, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=19, Name=Episode 19, Title=Episode 19, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=20, Name=Episode 20, Title=Episode 20, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=21, Name=Episode 21, Title=Episode 21, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=22, Name=Episode 22, Title=Episode 22, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=23, Name=Episode 23, Title=Episode 23, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=24, Name=Episode 24, Title=Episode 24, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=25, Name=Episode 25, Title=Episode 25, Status=Not yet available, Available=False
[UI] Episode data (6+ fields): ID=None, Number=26, Name=Episode 26, Title=Episode 26, Status=Not yet available, Available=False
[UI] Added 0 available episodes out of 0 total episodes shown
[UI] Added 0 episodes to list, continue button enabled: False